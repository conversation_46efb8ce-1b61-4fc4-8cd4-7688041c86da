#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to split large subtitle text files into smaller parts based on heuristic methods.

This script divides subtitle text files larger than 250,000 characters into roughly equal parts
between 125,000 and 250,000 characters each. The splitting occurs at natural transition points
identified by common transition words like "so", "okay", etc. The script attempts to find
the most natural breaking points while maintaining context.

The resulting parts are saved alongside the original file with _part1, _part2, etc. suffixes.
"""

import argparse
import logging
import re
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

# Constants
MAX_FILE_SIZE = 250000  # Characters - threshold for splitting files
# Target size for each part (75% of MAX_FILE_SIZE)
TARGET_PART_SIZE = int(MAX_FILE_SIZE * 0.75)
# Maximum allowed size for any part
MAX_PART_SIZE = MAX_FILE_SIZE
# Minimum preferred size for parts (50% of MAX_FILE_SIZE)
MIN_PART_SIZE = int(MAX_FILE_SIZE * 0.5)

# Multiplier for English text (English text can be longer than Russian for the same content)
ENGLISH_SIZE_MULTIPLIER = 1.8  # Default multiplier for English text

# Common English transition words that can serve as natural breaking points
# Level 1: High priority words that are most likely to indicate the start of a new thought or section
ENGLISH_TRANSITION_WORDS_LEVEL1 = {
    "so",
    "okay",
    "well",
    "now",
    "first",
    "second",
    "third",
    "finally",
    "lastly",
    "actually",
    "basically",
    "essentially",
    "honestly",
    "frankly",
    "anyway",
    "anyhow",
    "alright",
    "ok",
    "let's",
    "let us",
    "let me",
}

# Level 2: Lower priority words that may indicate transitions but are more common
ENGLISH_TRANSITION_WORDS_LEVEL2 = {
    "and",
    "but",
    "or",
    "because",
    "therefore",
    "thus",
    "hence",
    "consequently",
    "however",
    "nevertheless",
    "nonetheless",
    "although",
    "though",
    "besides",
    "furthermore",
    "moreover",
    "meanwhile",
    "subsequently",
    "eventually",
    "ultimately",
    "then",
}

# Common Russian transition words that can serve as natural breaking points
# Level 1: High priority words that are most likely to indicate the start of a new thought or section
RUSSIAN_TRANSITION_WORDS_LEVEL1 = {
    "итак",
    "так",
    "ладно",
    "окей",
    "теперь",
    "затем",
    "во-первых",
    "во-вторых",
    "в-третьих",
    "наконец",
    "в заключение",
    "фактически",
    "в основном",
    "по сути",
    "честно говоря",
    "откровенно",
    "давайте",
    "позвольте",
    "дальше",
    "далее",
    "начнем",
    "продолжим",
    "вернемся",
    "посмотрим",
    "рассмотрим",
    "перейдем",
}

# Level 2: Lower priority words that may indicate transitions but are more common
RUSSIAN_TRANSITION_WORDS_LEVEL2 = {
    "и",
    "но",
    "или",
    "потому",
    "поэтому",
    "следовательно",
    "таким образом",
    "в результате",
    "однако",
    "тем не менее",
    "несмотря на",
    "хотя",
    "впрочем",
    "в любом случае",
    "кроме того",
    "более того",
    "кстати",
    "между тем",
    "впоследствии",
    "в конечном счете",
    "в конце концов",
    "в итоге",
}

# Combine all transition words by level
TRANSITION_WORDS_LEVEL1 = ENGLISH_TRANSITION_WORDS_LEVEL1.union(
    RUSSIAN_TRANSITION_WORDS_LEVEL1
)
TRANSITION_WORDS_LEVEL2 = ENGLISH_TRANSITION_WORDS_LEVEL2.union(
    RUSSIAN_TRANSITION_WORDS_LEVEL2
)
TRANSITION_WORDS = TRANSITION_WORDS_LEVEL1.union(TRANSITION_WORDS_LEVEL2)

# Regex patterns to find transition words at the beginning of sentences or after punctuation
# These patterns look for transition words that are either:
# 1. At the beginning of a line
# 2. After a space and possibly preceded by punctuation
# The patterns are case-insensitive to catch both capitalized and lowercase words
TRANSITION_PATTERN_LEVEL1 = r"(?:^|\s+)({})[\s,.:]".format(
    "|".join(TRANSITION_WORDS_LEVEL1)
)
TRANSITION_PATTERN_LEVEL2 = r"(?:^|\s+)({})[\s,.:]".format(
    "|".join(TRANSITION_WORDS_LEVEL2)
)
TRANSITION_PATTERN = r"(?:^|\s+)({})[\s,.:]".format("|".join(TRANSITION_WORDS))


def detect_language(text: str) -> str:
    """
    Heuristically detect if the text is primarily Russian or English.

    The function counts the frequency of Cyrillic characters versus Latin characters
    to determine the dominant language in the text.

    Args:
        text: The text to analyze

    Returns:
        'russian' if the text is primarily Russian, 'english' otherwise
    """
    # Count Cyrillic characters (Russian alphabet)
    cyrillic_count = sum(
        1 for char in text if "а" <= char.lower() <= "я" or char.lower() in "ёй"
    )

    # Count Latin characters (English alphabet)
    latin_count = sum(1 for char in text if "a" <= char.lower() <= "z")

    # If there are significantly more Cyrillic characters than Latin, consider it Russian
    # Using a threshold to account for mixed-language texts
    if cyrillic_count > latin_count * 0.5:  # Threshold can be adjusted
        logger.debug(
            f"Detected Russian text (Cyrillic: {cyrillic_count}, Latin: {latin_count})"
        )
        return "russian"
    else:
        logger.debug(
            f"Detected English text (Cyrillic: {cyrillic_count}, Latin: {latin_count})"
        )
        return "english"


def get_size_constants(language: str) -> tuple[int, int, int, int]:
    """
    Get size constants adjusted for the detected language.

    For English text, the constants are multiplied by ENGLISH_SIZE_MULTIPLIER
    to account for the fact that English text tends to be longer than Russian
    for the same content.

    Args:
        language: The detected language ('russian' or 'english')

    Returns:
        Tuple of (MAX_FILE_SIZE, TARGET_PART_SIZE, MAX_PART_SIZE, MIN_PART_SIZE)
        adjusted for the detected language
    """
    # Get the current global constants
    global MAX_FILE_SIZE, TARGET_PART_SIZE, MAX_PART_SIZE, MIN_PART_SIZE

    if language == "english":
        # Apply multiplier for English text
        return (
            int(MAX_FILE_SIZE * ENGLISH_SIZE_MULTIPLIER),
            int(TARGET_PART_SIZE * ENGLISH_SIZE_MULTIPLIER),
            int(MAX_PART_SIZE * ENGLISH_SIZE_MULTIPLIER),
            int(MIN_PART_SIZE * ENGLISH_SIZE_MULTIPLIER),
        )
    else:
        # Use default constants for Russian text
        return (MAX_FILE_SIZE, TARGET_PART_SIZE, MAX_PART_SIZE, MIN_PART_SIZE)


def count_characters(file_path: str) -> int:
    """
    Count the number of characters in a file.

    Args:
        file_path: Path to the file

    Returns:
        Number of characters in the file
    """
    try:
        with open(file_path, encoding="utf-8") as file:
            content = file.read()
            return len(content)
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return 0


def find_transition_positions(content: str, level: int = 0) -> list[int]:
    """
    Find all positions of transition words in the content based on priority level.

    Args:
        content: File content as string
        level: Priority level (0 = all words, 1 = high priority only, 2 = low priority only)

    Returns:
        List of positions where transition words are found
    """
    transition_positions = [0]  # Start position

    # Select the appropriate pattern based on the level
    if level == 1:
        pattern = TRANSITION_PATTERN_LEVEL1
    elif level == 2:
        pattern = TRANSITION_PATTERN_LEVEL2
    else:  # level == 0 or any other value
        pattern = TRANSITION_PATTERN

    # Find all occurrences of transition words (case insensitive)
    for match in re.finditer(pattern, content, re.IGNORECASE):
        # Add the position where the actual transition word starts (not the whitespace before it)
        word_start = match.start() + match.group(0).index(match.group(1))
        transition_positions.append(word_start)

    # Add the end position
    transition_positions.append(len(content))

    return sorted(set(transition_positions))  # Remove duplicates and ensure order


def find_sentence_boundaries(content: str) -> list[int]:
    """
    Find positions that might be sentence boundaries (periods, question marks, exclamation points, etc.).
    This is a fallback method when transition words are insufficient.
    Works with both English and Russian text, including Russian-specific punctuation.

    Args:
        content: File content as string

    Returns:
        List of positions where sentence boundaries are found
    """
    boundary_positions = [0]  # Start position

    # Find all occurrences of potential sentence endings followed by spaces
    # This pattern includes standard punctuation and Russian-specific punctuation
    # Includes: period, exclamation mark, question mark, ellipsis, semicolon, colon
    for match in re.finditer(r"[.!?…;:][\s]+", content):
        boundary_positions.append(match.end())

    # Add the end position
    boundary_positions.append(len(content))

    return sorted(set(boundary_positions))  # Remove duplicates and ensure order


def find_word_boundaries(content: str) -> list[int]:
    """
    Find positions between words (spaces) as a last resort method.
    Works with both English and Russian text as both languages use spaces between words.

    Args:
        content: File content as string

    Returns:
        List of positions where word boundaries are found
    """
    boundary_positions = [0]  # Start position

    # Find all occurrences of spaces
    # This pattern works for both English and Russian text as both use spaces between words
    for match in re.finditer(r"\s+", content):
        boundary_positions.append(match.end())

    # Add the end position
    boundary_positions.append(len(content))

    return sorted(set(boundary_positions))  # Remove duplicates and ensure order


def determine_split_points(content: str) -> list[int]:
    """
    Determine optimal split points based on transition words and target size.
    Uses a tiered approach:
    1. First tries high-priority transition words (level 1)
    2. If not enough, adds low-priority transition words (level 2)
    3. If still not enough, adds sentence boundaries
    4. As a last resort, adds word boundaries

    The function automatically detects the language of the content and adjusts
    the size constants accordingly.

    Args:
        content: File content as string

    Returns:
        List of positions where the file should be split
    """
    total_length = len(content)

    # Detect language and get appropriate size constants
    language = detect_language(content)
    max_file_size, target_part_size, max_part_size, min_part_size = get_size_constants(
        language
    )

    logger.info(
        f"Detected language: {language}, applying size multiplier: {ENGLISH_SIZE_MULTIPLIER if language == 'english' else 1.0}"
    )
    logger.debug(
        f"Adjusted constants - MAX_FILE_SIZE: {max_file_size}, TARGET_PART_SIZE: {target_part_size}, MAX_PART_SIZE: {max_part_size}, MIN_PART_SIZE: {min_part_size}"
    )

    # If the file is small enough, return it as is
    if total_length <= max_file_size:
        return [0, total_length]

    # Try to find high-priority transition words first (level 1)
    level1_positions = find_transition_positions(content, level=1)
    logger.debug(f"Found {len(level1_positions) - 2} level 1 transition words")

    # If we don't have enough high-priority transition words, add low-priority ones (level 2)
    all_positions = level1_positions
    if len(level1_positions) < 5:  # Arbitrary threshold
        level2_positions = find_transition_positions(content, level=2)
        logger.debug(f"Found {len(level2_positions) - 2} level 2 transition words")
        all_positions = sorted(set(level1_positions + level2_positions))

    # If we still don't have enough transition words, also look for sentence boundaries
    if len(all_positions) < 5:  # Arbitrary threshold
        sentence_positions = find_sentence_boundaries(content)
        logger.debug(f"Found {len(sentence_positions) - 2} sentence boundaries")
        all_positions = sorted(set(all_positions + sentence_positions))

    # If we still don't have enough potential split points, use word boundaries
    if len(all_positions) < 10:  # Another arbitrary threshold
        word_positions = find_word_boundaries(content)
        logger.debug(f"Found {len(word_positions) - 2} word boundaries")
        all_positions = sorted(set(all_positions + word_positions))

    # Skip the first and last positions (they're just the start and end)
    usable_positions = all_positions[1:-1]

    if not usable_positions:
        logger.warning("No usable split points found, cannot split the file")
        return [0, total_length]

    # Calculate minimum number of parts needed to ensure no part exceeds max_part_size
    min_parts_needed = (
        total_length + max_part_size - 1
    ) // max_part_size  # Ceiling division

    # Calculate ideal number of parts based on target_part_size
    ideal_parts = max(
        min_parts_needed, (total_length + target_part_size - 1) // target_part_size
    )

    logger.debug(
        f"File size: {total_length}, minimum parts needed: {min_parts_needed}, ideal parts: {ideal_parts}"
    )

    # Calculate the ideal part size
    ideal_part_size = total_length / ideal_parts

    # Strategy: Try to create roughly equal parts using the identified positions
    split_points = [0]  # Start with the beginning of the file
    next_ideal_position = ideal_part_size

    while (
        next_ideal_position < total_length - min_part_size
    ):  # Ensure last part is not too small
        # Find the position closest to the ideal position
        best_position = None
        best_distance = float("inf")

        for pos in usable_positions:
            # Skip positions we've already passed
            if pos <= split_points[-1]:
                continue

            # Skip positions that would create a part smaller than min_part_size
            if pos - split_points[-1] < min_part_size:
                continue

            # Skip positions that would create a part larger than max_part_size
            if pos - split_points[-1] > max_part_size:
                continue

            # Calculate distance to ideal position
            distance = abs(pos - (split_points[-1] + ideal_part_size))

            if distance < best_distance:
                best_distance = distance
                best_position = pos

        # If we found a suitable position, use it
        if best_position is not None:
            split_points.append(best_position)
            next_ideal_position = split_points[-1] + ideal_part_size
        else:
            # If we can't find a suitable position, try to find any position that works
            # This is a fallback strategy
            for pos in sorted(usable_positions):
                if pos > split_points[-1] and pos - split_points[-1] >= min_part_size:
                    split_points.append(pos)
                    next_ideal_position = split_points[-1] + ideal_part_size
                    logger.warning(f"Using fallback split point at position {pos}")
                    break
            else:
                # If we still can't find a suitable position, we have to stop
                logger.warning("Could not find suitable positions for splitting")
                break

    # Add the end of the file if it's not already included
    if split_points[-1] != total_length:
        split_points.append(total_length)

    # If we couldn't create at least two parts, return the whole file
    if len(split_points) <= 2:
        logger.warning("Could not split the file into multiple parts")
        return [0, total_length]

    # Verify no part exceeds max_part_size
    for i in range(len(split_points) - 1):
        part_size = split_points[i + 1] - split_points[i]
        if part_size > max_part_size:
            logger.warning(
                f"Part {i + 1} size ({part_size}) exceeds maximum allowed size ({max_part_size})"
            )

    return split_points


def split_file(file_path: str) -> bool:
    """
    Split a file into parts based on transition words and size constraints.
    The function automatically detects the language of the content and adjusts
    the size constants accordingly.

    Args:
        file_path: Path to the file to split

    Returns:
        True if the file was split, False otherwise
    """
    try:
        # Read file content first to detect language
        with open(file_path, encoding="utf-8") as file:
            content = file.read()

        # Detect language and get appropriate size constants
        language = detect_language(content)
        max_file_size, target_part_size, max_part_size, min_part_size = (
            get_size_constants(language)
        )

        # Check if file needs splitting
        file_size = len(content)
        if file_size <= max_file_size:
            logger.info(
                f"File {file_path} is smaller than {max_file_size} characters ({language} text), skipping."
            )
            return False

        logger.info(
            f"Processing file: {file_path} ({file_size} characters, {language} text)"
        )

        # Determine split points (language detection happens inside this function)
        split_points = determine_split_points(content)

        # If we couldn't find good split points, log and return
        if len(split_points) <= 2:
            logger.warning(f"Could not find suitable split points for {file_path}")
            return False

        # Split the file and save parts
        file_path_obj = Path(file_path)
        base_name = file_path_obj.stem
        extension = file_path_obj.suffix
        directory = file_path_obj.parent

        # Check if any part exceeds the maximum size
        for i in range(len(split_points) - 1):
            part_size = split_points[i + 1] - split_points[i]
            if part_size > max_part_size:
                logger.warning(
                    f"Part {i + 1} size ({part_size}) exceeds maximum allowed size ({max_part_size})"
                )
                return False  # Don't proceed if any part is too large

        for i in range(len(split_points) - 1):
            start = split_points[i]
            end = split_points[i + 1]

            # Extract content for this part
            part_content = content[start:end]

            # Create part filename
            part_filename = f"{base_name}_part{i + 1}{extension}"
            part_path = directory / part_filename

            # Save part
            with open(part_path, "w", encoding="utf-8") as part_file:
                part_file.write(part_content)

            logger.info(
                f"Created part {i + 1}: {part_path} ({len(part_content)} characters)"
            )

        return True

    except Exception as e:
        logger.error(f"Error splitting file {file_path}: {e}")
        return False


def process_directory(directory: str, pattern: str = "*.txt") -> tuple[int, int]:
    """
    Process all subtitle text files in a directory.

    Args:
        directory: Directory to process
        pattern: File pattern to match (default: "*.txt")

    Returns:
        Tuple of (number of files processed, number of files split)
    """
    directory_path = Path(directory)
    if not directory_path.exists() or not directory_path.is_dir():
        logger.error(f"Directory {directory} does not exist or is not a directory")
        return 0, 0

    # Find all text files in the directory
    file_paths = list(directory_path.glob(pattern))

    if not file_paths:
        logger.warning(f"No files matching pattern '{pattern}' found in {directory}")
        return 0, 0

    logger.info(
        f"Found {len(file_paths)} files matching pattern '{pattern}' in {directory}"
    )

    # Process each file
    files_processed = 0
    files_split = 0

    for file_path in file_paths:
        # Skip files that are already parts
        if re.search(r"_part\d+", file_path.stem):
            logger.info(f"Skipping part file: {file_path}")
            continue

        files_processed += 1
        if split_file(str(file_path)):
            files_split += 1

    return files_processed, files_split


def main():
    """
    Main function to parse arguments and process files.
    """
    parser = argparse.ArgumentParser(
        description="Split large subtitle text files into smaller parts based on transition words."
    )
    parser.add_argument(
        "directory",
        nargs="?",
        default=".",
        help="Directory containing subtitle text files to process (default: current directory)",
    )
    parser.add_argument(
        "--pattern", default="*.txt", help="File pattern to match (default: *.txt)"
    )
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    parser.add_argument(
        "--english-multiplier",
        type=float,
        default=1.8,
        help="Multiplier for size constants when processing English text (default: 1.8)",
    )
    parser.add_argument(
        "--maxtarget",
        type=int,
        default=250000,
        help="Maximum file size threshold for splitting (default: 250000 characters)",
    )

    args = parser.parse_args()

    # Set logging level based on verbose flag
    if args.verbose:
        logger.setLevel(logging.DEBUG)

    # Set the English size multiplier
    global ENGLISH_SIZE_MULTIPLIER
    ENGLISH_SIZE_MULTIPLIER = args.english_multiplier
    logger.info(f"English size multiplier set to: {ENGLISH_SIZE_MULTIPLIER}")

    # Set the MAX_FILE_SIZE and related constants
    global MAX_FILE_SIZE, TARGET_PART_SIZE, MAX_PART_SIZE, MIN_PART_SIZE
    MAX_FILE_SIZE = args.maxtarget
    TARGET_PART_SIZE = int(MAX_FILE_SIZE * 0.75)  # 75% of MAX_FILE_SIZE
    MAX_PART_SIZE = MAX_FILE_SIZE
    MIN_PART_SIZE = int(MAX_FILE_SIZE * 0.5)  # 50% of MAX_FILE_SIZE
    logger.info(f"Maximum file size set to: {MAX_FILE_SIZE} characters")
    logger.info(
        f"Target part size set to: {TARGET_PART_SIZE} characters (75% of maximum)"
    )
    logger.info(
        f"Minimum part size set to: {MIN_PART_SIZE} characters (50% of maximum)"
    )

    # Process the directory
    files_processed, files_split = process_directory(args.directory, args.pattern)

    # Print summary
    logger.info(
        f"Summary: Processed {files_processed} files, split {files_split} files"
    )


if __name__ == "__main__":
    main()
