import asyncio
import json
import os
import subprocess
import sys
from pathlib import Path

import mdformat
import websockets
from rich.console import Console


class APIClient:
    """Клиент для работы с API сервера суммаризации"""

    def __init__(
        self,
        host: str = "localhost",
        port: int = 8000,
        api_key: str = None,
        debug: bool = False,
    ):
        self.host = host
        self.port = port
        self.base_url = f"http://{self.host}:{self.port}"
        self.api_key = api_key
        self.debug = debug
        self.console = Console()
        self.server_process = None
        self.reconnect_attempts = 3
        self.reconnect_delay = 5
        self.connection_timeout = 60

        # Configure WebSocket URL with authentication if API key is provided
        if self.api_key:
            self.ws_url = (
                f"ws://{self.host}:{self.port}/ws/summarize?token={self.api_key}"
            )
            self._log_debug(
                f"WebSocket URL configured with authentication: ws://{self.host}:{self.port}/ws/summarize?token=***"
            )
        else:
            self.ws_url = f"ws://{self.host}:{self.port}/ws/summarize"
            self._log_debug("WebSocket URL configured without authentication")

        self._log_debug(
            f"APIClient initialized. Host: {self.host}, Port: {self.port}, API Key: {'***' if self.api_key else 'None'}"
        )

    def _log_debug(self, message: str, exc_info: bool = False):
        """Логирование отладочных сообщений"""
        if self.debug:
            # Use the logger instance defined in the main script
            import logging

            logger = logging.getLogger(__name__)
            logger.debug(message, exc_info=exc_info)

    async def check_server_available(self) -> bool:
        """Проверка доступности сервера через POST запрос к эндпоинту /ping"""
        self._log_debug("Checking server availability...")
        try:
            import aiohttp

            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/ping", timeout=2) as response:
                    response_json = await response.json()
                    self._log_debug(
                        f"Server check response status: {response.status}, message: {response_json}"
                    )
                    return (
                        response.status == 200
                        and response_json.get("message") == "pong"
                    )
        except Exception as e:
            self._log_debug(f"Server check failed or server not running: {e}")
            self.console.print(
                "[blue]Сторонний сервер не запущен. Запуск нового экземпляра FastAPI.[/blue]"
            )
            return False

    async def start_server(self):
        """Запуск FastAPI сервера"""
        self._log_debug(
            "Attempting to start local server instance (if not already running)."
        )
        # Проверяем, доступен ли уже сервер
        if await self.check_server_available():
            self.console.print("[green]Сервер уже запущен и доступен[/green]")
            self._log_debug("Server already running and available.")
            return

        try:
            # Запуск сервера в отдельном процессе
            server_script = Path(__file__).parent.parent / "main.py"
            self._log_debug(f"Starting server script: {server_script}")
            self.server_process = subprocess.Popen(
                [sys.executable, str(server_script)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )
            self._log_debug(
                f"Server process started with PID: {self.server_process.pid if self.server_process else 'N/A'}"
            )
            # Ждем немного, чтобы сервер успел запуститься
            await asyncio.sleep(3)
        except Exception as e:
            self.console.print(f"[red]Ошибка при запуске сервера: {e}[/red]")
            self._log_debug(f"Error during server startup: {e}", exc_info=True)
            sys.exit(1)

    def cleanup(self):
        """Очистка ресурсов при завершении"""
        self._log_debug("Cleanup called.")
        if self.server_process:
            self._log_debug(
                f"Terminating server process with PID: {self.server_process.pid}"
            )
            self.server_process.terminate()
            self.server_process.wait()
            self._log_debug("Server process terminated.")

    async def process_file(
        self,
        file_path: Path,
        mode: str,
        format_markdown: bool = False,
        progress=None,
        task_id=None,
    ) -> bool:
        """Обработка одного файла"""
        self._log_debug(f"Processing file: {file_path}")
        try:
            base_name = file_path.stem
            md_path = (
                file_path.parent / f"{base_name}-{mode}.md"
                if mode != "default"
                else file_path.parent / f"{base_name}.md"
            )
            if md_path.exists():
                if progress and task_id is not None:
                    progress.update(
                        task_id,
                        description=f"[yellow]Пропущен (MD существует): {file_path.name}[/yellow]",
                    )
                return False

            text = file_path.read_text(encoding="utf-8")
            if not text.strip():
                if progress and task_id is not None:
                    progress.update(
                        task_id,
                        description=f"[red]Пропущен (пустой файл): {file_path.name}[/red]",
                    )
                return False

            for attempt in range(self.reconnect_attempts):
                self._log_debug(
                    f"Attempting WebSocket connection to {self.ws_url} (Attempt {attempt + 1}/{self.reconnect_attempts}) for file {file_path.name}"
                )
                try:
                    async with websockets.connect(
                        self.ws_url,
                        close_timeout=10,
                    ) as ws:
                        self._log_debug(
                            f"WebSocket connected to {self.ws_url} for file {file_path.name}"
                        )
                        current_task_id_from_server = None

                        initial_request_data = {
                            "type": "text_input",  # This matches MessageType.TEXT.value
                            "content": text,
                            "mode": mode,
                            # client_uid is deprecated and no longer sent
                        }
                        initial_request_json = json.dumps(initial_request_data)
                        self._log_debug(
                            f"Sending task request to server for {file_path.name}: {initial_request_json}"
                        )
                        await ws.send(initial_request_json)

                        try:
                            while True:
                                try:
                                    response = await asyncio.wait_for(
                                        ws.recv(), timeout=self.connection_timeout
                                    )
                                    self._log_debug(
                                        f"Received raw message from server for {file_path.name}: {response}"
                                    )
                                    data = json.loads(response)

                                    if data.get("type") == "ping" and "task_id" in data:
                                        server_task_id_ping = data["task_id"]
                                        self._log_debug(
                                            f"Received PING from server for task_id: {server_task_id_ping} (file: {file_path.name})"
                                        )
                                        pong_message = {
                                            "type": "pong",
                                            "task_id": server_task_id_ping,
                                        }
                                        pong_message_json = json.dumps(pong_message)
                                        self._log_debug(
                                            f"Sending PONG to server for {file_path.name}: {pong_message_json}"
                                        )
                                        await ws.send(pong_message_json)
                                        continue

                                    server_task_id = data.get("task_id")
                                    status = data.get("status")
                                    self._log_debug(
                                        f"Received status update for {file_path.name}: {status}, Task ID from server: {server_task_id}"
                                    )

                                    if server_task_id and server_task_id != "error":
                                        if (
                                            current_task_id_from_server
                                            != server_task_id
                                        ):
                                            current_task_id_from_server = server_task_id
                                            self._log_debug(
                                                f"Client now associated with server task_id: {current_task_id_from_server} for file {file_path.name}"
                                            )

                                    if status == "completed" and data.get("summary"):
                                        # Сохраняем результат
                                        await self._save_summary(
                                            data["summary"],
                                            md_path,
                                            file_path,
                                            format_markdown,
                                        )
                                        self._log_debug(
                                            f"Summary successfully saved to {md_path} for file {file_path.name}"
                                        )

                                        if progress and task_id is not None:
                                            progress.update(
                                                task_id,
                                                description=f"[green]Обработан ({mode}): {file_path.name}[/green]",
                                            )
                                        return True
                                    elif status == "failed":
                                        error = data.get("error", "Неизвестная ошибка")
                                        self._log_debug(
                                            f"Task failed for {file_path.name}. Error: {error}"
                                        )
                                        if progress and task_id is not None:
                                            progress.update(
                                                task_id,
                                                description=f"[red]Ошибка ({file_path.name}): {error}[/red]",
                                            )
                                        return False
                                    else:
                                        if progress and task_id is not None:
                                            progress.update(
                                                task_id,
                                                description=f"Обработка: {file_path.name}",
                                            )
                                except TimeoutError:
                                    self._log_debug(
                                        f"Timeout waiting for server message for {file_path.name}. Connection timeout: {self.connection_timeout}s. Server should have sent ping within this if alive."
                                    )
                                    continue
                        except Exception as e:
                            self._log_debug(
                                f"Exception during WebSocket communication for {file_path.name}: {e}",
                                exc_info=True,
                            )
                            raise e  # Re-raise to be caught by outer try-except for reconnect logic

                except (websockets.exceptions.ConnectionClosed, ConnectionError) as e:
                    # Handle authentication errors
                    if hasattr(e, "code"):
                        if e.code == 4001:
                            self._handle_auth_error(
                                e, file_path, progress, task_id, "4001"
                            )
                            return False
                        elif e.code == 4000:
                            self._handle_auth_error(
                                e, file_path, progress, task_id, "4000"
                            )
                            return False

                    self._log_debug(
                        f"Connection error for {file_path.name} (Attempt {attempt + 1}): {e}",
                        exc_info=True,
                    )
                    if attempt < self.reconnect_attempts - 1:
                        wait_time = self.reconnect_delay * (attempt + 1)
                        self._log_debug(
                            f"Connection error for {file_path.name}. Retrying in {wait_time}s. Attempt {attempt + 1}/{self.reconnect_attempts}"
                        )
                        if progress and task_id is not None:
                            progress.update(
                                task_id,
                                description=f"[yellow]Переподключение ({attempt + 1}/{self.reconnect_attempts}): {file_path.name}[/yellow]",
                            )
                        await asyncio.sleep(wait_time)
                        continue

                    self._log_debug(
                        f"Failed to connect after {self.reconnect_attempts} attempts for {file_path.name}."
                    )
                    if progress and task_id is not None:
                        progress.update(
                            task_id,
                            description=f"[red]Ошибка: Сервер недоступен после {self.reconnect_attempts} попыток подключения[/red]",
                        )
                    # Завершаем работу клиента при потере связи с сервером
                    self.cleanup()
                    sys.exit(1)
        except Exception as e:
            self._log_debug(
                f"Unhandled error in process_file for {file_path.name}: {e}",
                exc_info=True,
            )
            if progress and task_id is not None:
                progress.update(
                    task_id,
                    description=f"[red]Ошибка ({file_path.name}): {str(e)}[/red]",
                )
            return False

    async def _save_summary(
        self, summary: str, md_path: Path, file_path: Path, format_markdown: bool
    ):
        """Сохранение результата суммаризации в файл"""
        if format_markdown:
            # Форматируем Markdown текст с помощью mdformat
            formatted_text = mdformat.text(
                summary,
                options={
                    "wrap": "no",
                    "number": True,
                    "markdown_flavor": "gfm",
                },
            )
            md_path.write_text(formatted_text, encoding="utf-8")
        else:
            # Сохраняем текст без форматирования
            md_path.write_text(summary, encoding="utf-8")

        # Копируем временные метки из исходного файла
        source_stat = os.stat(file_path)
        os.utime(
            md_path,
            (
                source_stat.st_atime,
                source_stat.st_mtime,
            ),
        )

    def _handle_auth_error(
        self, e, file_path: Path, progress, task_id, error_code: str
    ):
        """Обработка ошибок аутентификации"""
        self._log_debug(
            f"Authentication error ({error_code}) for {file_path.name}: {e}"
        )

        if progress and task_id is not None:
            if error_code == "4001":
                progress.update(
                    task_id,
                    description=f"[red]Ошибка аутентификации: {file_path.name} - Проверьте API ключ[/red]",
                )
            else:
                progress.update(
                    task_id,
                    description=f"[red]Ошибка аутентификации: {file_path.name}[/red]",
                )

        if error_code == "4001":
            self.console.print(
                f"[red]❌ Ошибка аутентификации: {e.reason if hasattr(e, 'reason') else 'Неверный API ключ или недостаточно прав'}[/red]"
            )
            if self.api_key:
                self.console.print(
                    "[yellow]💡 Проверьте правильность API ключа и наличие прав 'write'[/yellow]"
                )
            else:
                self.console.print(
                    "[yellow]💡 Возможно, требуется API ключ. Используйте --api-key[/yellow]"
                )
        else:
            self.console.print(
                f"[red]❌ Ошибка аутентификации: {e.reason if hasattr(e, 'reason') else 'Общая ошибка аутентификации'}[/red]"
            )

        self.cleanup()
        sys.exit(1)
