"""
Subtitle extraction endpoints.
"""

import asyncio
import builtins
from typing import Any

from fastapi import APIRouter, Depends, Request
from fastapi.responses import JSONResponse
from loguru import logger

from api.middleware.auth import require_write
from api.middleware.error_handler import (
    ServiceUnavailableError,
    TimeoutError,
    ValidationError,
)
from models.schemas import (
    SubtitleRequest,
    SubtitleResponse,
    TaskStatus,
    VideoListRequest,
    VideoListResponse,
)

router = APIRouter()


def _check_server_overload(task_queue):
    """Check if server is overloaded and raise appropriate error."""
    active_tasks_count = len(task_queue.active_async_tasks)
    max_total_workers = (
        task_queue.LIMIT_SUBTITLE_WORKERS + task_queue.LIMIT_SUMMARIZE_WORKERS
    )
    is_subtitle_queue_full = task_queue.subtitle_queue.full()

    if is_subtitle_queue_full:
        raise ServiceUnavailableError(
            "Subtitle task queue is full. Please try again later."
        )

    if active_tasks_count >= max_total_workers:
        raise ServiceUnavailableError(
            "Server is at maximum processing capacity. Please try again later."
        )


@router.post("/subtitles/get_id", response_model=VideoListResponse)
async def get_video_ids(
    request: VideoListRequest,
    req: Request,
    user: dict[str, Any] = Depends(require_write),
):
    """
    Submit a YouTube URL to get list of video IDs.

    Requires: write permission
    """
    task_queue = req.app.state.task_queue

    # Note: client_uid is deprecated and ignored in processing
    if request.client_uid:
        logger.debug(
            f"Received deprecated client_uid: {request.client_uid} (ignored for processing)"
        )

    logger.debug(f"Received /subtitles/get_id request for URL: {request.url}")

    # Check server overload (will raise ServiceUnavailableError if overloaded)
    _check_server_overload(task_queue)

    try:
        logger.debug(
            f"Attempting to add task for {request.url} to video_list_task (via subtitle queue)."
        )
        response = await asyncio.wait_for(
            task_queue.add_video_list_task(
                str(request.url),
                client_uid=None,  # Explicitly set to None - deprecated field
            ),
            timeout=5.0,
        )
        logger.debug(
            f"Video list task added for {request.url}. Response from add_video_list_task: ID {response.task_id}, Status {response.status}"
        )
        status_code = 200 if response.status == TaskStatus.COMPLETED else 202
        return JSONResponse(status_code=status_code, content=response.model_dump())

    except builtins.TimeoutError:
        logger.error(
            f"Timeout adding video list task to queue for URL: {request.url}",
            exc_info=True,
        )
        raise TimeoutError("Server is busy, please try again later")

    except ValueError as e:
        # Invalid YouTube URLs
        raise ValidationError(str(e))


@router.post("/subtitles", response_model=SubtitleResponse)
async def create_subtitles_task(
    request: SubtitleRequest,
    req: Request,
    user: dict[str, Any] = Depends(require_write),
):
    """
    Submit a new YouTube URL for subtitle extraction.

    Requires: write permission
    """
    task_queue = req.app.state.task_queue

    # Note: client_uid is deprecated and ignored in processing
    if request.client_uid:
        logger.debug(
            f"Received deprecated client_uid: {request.client_uid} (ignored for processing)"
        )

    logger.debug(f"Received /subtitles request for URL: {request.url}")

    # Check server overload (will raise ServiceUnavailableError if overloaded)
    _check_server_overload(task_queue)

    try:
        logger.debug(
            f"Attempting to add task for {request.url} to subtitle task queue."
        )
        response = await asyncio.wait_for(
            task_queue.add_subtitle_task(
                str(request.url),
                client_uid=None,  # Explicitly set to None - deprecated field
            ),
            timeout=5.0,
        )
        logger.debug(
            f"Subtitle task added for {request.url}. Response from add_subtitle_task: ID {response.task_id}, Status {response.status}"
        )
        status_code = 200 if response.status == TaskStatus.COMPLETED else 202
        return JSONResponse(status_code=status_code, content=response.model_dump())

    except builtins.TimeoutError:
        logger.error(
            f"Timeout adding subtitle task to queue for URL: {request.url}",
            exc_info=True,
        )
        raise TimeoutError("Server is busy, please try again later")

    except ValueError as e:
        # Invalid YouTube URLs
        raise ValidationError(str(e))
