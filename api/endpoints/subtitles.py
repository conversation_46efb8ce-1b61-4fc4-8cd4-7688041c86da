"""API endpoints for subtitle-related operations."""

import asyncio
import logging

from fastapi import status
from fastapi.responses import JSONResponse

from models.schemas import (
    SubtitleRequest,
    SubtitleResponse,
    TaskStatus,
    VideoListRequest,
    VideoListResponse,
)

from .base import BaseRouter

logger = logging.getLogger(__name__)


class SubtitleRouter(BaseRouter):
    """Router for subtitle-related endpoints."""

    def __init__(self):
        super().__init__(prefix="/subtitles", tags=["Subtitles"])
        self._setup_routes()

    def _setup_routes(self) -> None:
        """Set up the routes for this router."""
        self.router.add_api_route(
            "/get_id",
            self.get_video_ids,
            methods=["POST"],
            response_model=VideoListResponse,
            status_code=status.HTTP_200_OK,
        )

        self.router.add_api_route(
            "",
            self.create_subtitles_task,
            methods=["POST"],
            response_model=SubtitleResponse,
            status_code=status.HTTP_202_ACCEPTED,
        )

    async def get_video_ids(self, request: VideoListRequest) -> JSONResponse:
        """
        Submit a YouTube URL to get a list of video IDs.

        Args:
            request: The request containing the YouTube URL and client UID.

        Returns:
            JSONResponse: The response containing the task ID and status.
        """
        logger.debug(
            f"Received /subtitles/get_id request for URL: {request.url} with "
            f"client_uid: {request.client_uid}"
        )

        task_queue = self._get_task_queue()

        # Check if server is overloaded
        if await self._check_overload():
            return self._create_error_response(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                error="Server is currently overloaded. Please try again later.",
                status=TaskStatus.FAILED,
            )

        # Check if subtitle queue is full
        if task_queue.subtitle_queue.full():
            logger.debug("Subtitle task queue is full")
            return self._create_error_response(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                error="Subtitle task queue is full. Please try again later.",
                status=TaskStatus.FAILED,
            )

        try:
            # Add task to queue
            response = await asyncio.wait_for(
                task_queue.add_video_list_task(
                    str(request.url), client_uid=request.client_uid
                ),
                timeout=5.0,
            )

            logger.debug(
                f"Video list task added for {request.url}. "
                f"Response: ID {response.task_id}, Status {response.status}"
            )

            # Return appropriate status code based on task status
            status_code = (
                status.HTTP_200_OK
                if response.status == TaskStatus.COMPLETED
                else status.HTTP_202_ACCEPTED
            )

            return JSONResponse(status_code=status_code, content=response.model_dump())

        except TimeoutError:
            logger.error(
                f"Timeout adding video list task to queue for URL: {request.url}",
                exc_info=True,
            )
            return self._create_error_response(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                error="Server is busy, please try again later",
                status=TaskStatus.FAILED,
            )

        except ValueError as e:
            # Invalid YouTube URL
            logger.warning(f"Invalid YouTube URL: {request.url}")
            return self._create_error_response(
                status_code=status.HTTP_400_BAD_REQUEST,
                error=str(e),
                status=TaskStatus.FAILED,
            )

    async def create_subtitles_task(self, request: SubtitleRequest) -> JSONResponse:
        """
        Submit a new YouTube URL for subtitle extraction.

        Args:
            request: The request containing the YouTube URL and client UID.

        Returns:
            JSONResponse: The response containing the task ID and status.
        """
        logger.debug(
            f"Received /subtitles request for URL: {request.url} with "
            f"client_uid: {request.client_uid}"
        )

        task_queue = self._get_task_queue()

        # Check if server is overloaded
        if await self._check_overload():
            return self._create_error_response(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                error="Server is currently overloaded. Please try again later.",
                status=TaskStatus.FAILED,
            )

        # Check if subtitle queue is full
        if task_queue.subtitle_queue.full():
            logger.debug("Subtitle task queue is full")
            return self._create_error_response(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                error="Subtitle task queue is full. Please try again later.",
                status=TaskStatus.FAILED,
            )

        try:
            # Add task to queue
            response = await asyncio.wait_for(
                task_queue.add_subtitle_task(
                    str(request.url), client_uid=request.client_uid
                ),
                timeout=5.0,
            )

            logger.debug(
                f"Subtitle task added for {request.url}. "
                f"Response: ID {response.task_id}, Status {response.status}"
            )

            # Return appropriate status code based on task status
            status_code = (
                status.HTTP_200_OK
                if response.status == TaskStatus.COMPLETED
                else status.HTTP_202_ACCEPTED
            )

            return JSONResponse(status_code=status_code, content=response.model_dump())

        except TimeoutError:
            logger.error(
                f"Timeout adding subtitle task to queue for URL: {request.url}",
                exc_info=True,
            )
            return self._create_error_response(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                error="Server is busy, please try again later",
                status=TaskStatus.FAILED,
            )

        except ValueError as e:
            # Invalid YouTube URL
            logger.warning(f"Invalid YouTube URL: {request.url}")
            return self._create_error_response(
                status_code=status.HTTP_400_BAD_REQUEST,
                error=str(e),
                status=TaskStatus.FAILED,
            )


# Create router instance
router = SubtitleRouter().router
