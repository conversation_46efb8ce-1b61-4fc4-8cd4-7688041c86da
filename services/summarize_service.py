"""Service for handling text summarization operations."""

import logging
from typing import Any

from models.schemas import (
    SummarizeMode,
    SummarizeRequest,
    SummarizeResponse,
    TaskStatus,
)
from services.base import BaseService
from worker.queue import TaskQueue

logger = logging.getLogger(__name__)


class SummarizeService(BaseService):
    """Service for handling text summarization operations."""

    def __init__(self, task_queue: TaskQueue):
        """
        Initialize the summarize service.

        Args:
            task_queue: The task queue for managing background tasks.
        """
        super().__init__()
        self.task_queue = task_queue

    async def create_summarize_task(
        self, request: SummarizeRequest
    ) -> SummarizeResponse:
        """
        Create a new text summarization task.

        Args:
            request: The request containing the text to summarize and options.

        Returns:
            SummarizeResponse: The response containing the task ID and status.
        """
        # Note: client_uid is deprecated and ignored in processing
        if request.client_uid:
            self.logger.debug(
                f"Received deprecated client_uid: {request.client_uid} (ignored for processing)"
            )

        self.logger.debug(f"Creating summarize task with mode: {request.mode}")

        try:
            # Add task to queue
            response = await self.task_queue.add_summarize_task(
                text=request.text, mode=request.mode, client_uid=None  # Explicitly set to None - deprecated field
            )

            self.logger.debug(
                f"Summarize task added. Response: ID {response.task_id}, "
                f"Status {response.status}"
            )

            return response

        except Exception as e:
            self.logger.error(f"Error creating summarize task: {e}", exc_info=True)
            raise

    async def create_summarize_file_task(
        self,
        file_content: str | bytes,
        file_name: str,
        mode: SummarizeMode | None = None,
        client_uid: str | None = None,  # Deprecated: kept for backward compatibility only
    ) -> SummarizeResponse:
        """
        Create a new text summarization task from a file.

        Args:
            file_content: The content of the file to summarize.
            file_name: The name of the file.
            mode: The summarization mode.
            client_uid: Optional client UID for tracking (DEPRECATED - ignored in processing).

        Returns:
            SummarizeResponse: The response containing the task ID and status.
        """
        # Note: client_uid is deprecated and ignored in processing
        if client_uid:
            self.logger.debug(
                f"Received deprecated client_uid: {client_uid} (ignored for processing)"
            )

        self.logger.debug(
            f"Creating summarize file task for file: {file_name} (mode: {mode})"
        )

        try:
            # Convert bytes to string if needed
            if isinstance(file_content, bytes):
                file_content = file_content.decode("utf-8")

            # Add task to queue
            response = await self.task_queue.add_summarize_task(
                text=file_content, mode=mode, client_uid=None, file_name=file_name  # Explicitly set to None - deprecated field
            )

            self.logger.debug(
                f"Summarize file task added. Response: ID {response.task_id}, "
                f"Status {response.status}"
            )

            return response

        except UnicodeDecodeError as e:
            self.logger.error("Failed to decode file content as UTF-8", exc_info=True)
            raise ValueError("File must be a text file encoded in UTF-8") from e

        except Exception as e:
            self.logger.error(f"Error creating summarize file task: {e}", exc_info=True)
            raise

    async def get_task_status(self, task_id: str) -> dict[str, Any]:
        """
        Get the status of a summarization task.

        Args:
            task_id: The ID of the task to check.

        Returns:
            Dict containing the task status and result if available.
        """
        self.logger.debug(f"Getting status for task: {task_id}")

        try:
            task_status = await self.task_queue.get_task_status(task_id)

            if task_status is None:
                return {
                    "status": TaskStatus.NOT_FOUND,
                    "error": f"Task {task_id} not found",
                }

            return {
                "status": task_status.status,
                "progress": task_status.progress,
                "result": task_status.result,
                "error": task_status.error,
                "created_at": (
                    task_status.created_at.isoformat()
                    if task_status.created_at
                    else None
                ),
                "updated_at": (
                    task_status.updated_at.isoformat()
                    if task_status.updated_at
                    else None
                ),
                "completed_at": (
                    task_status.completed_at.isoformat()
                    if task_status.completed_at
                    else None
                ),
            }

        except Exception as e:
            self.logger.error(
                f"Error getting task status for {task_id}: {e}", exc_info=True
            )
            return {
                "status": TaskStatus.FAILED,
                "error": f"Error getting task status: {str(e)}",
            }

    async def execute(self, *args: Any, **kwargs: Any) -> Any:
        """
        Execute the service operation.

        This is a generic method that routes to the appropriate method based on the operation.

        Args:
            *args: Positional arguments for the service.
            **kwargs: Keyword arguments for the service.

        Returns:
            The result of the service operation.
        """
        operation = kwargs.pop("operation", None)

        if operation == "create_summarize_task":
            return await self.create_summarize_task(*args, **kwargs)
        elif operation == "create_summarize_file_task":
            return await self.create_summarize_file_task(*args, **kwargs)
        elif operation == "get_task_status":
            return await self.get_task_status(*args, **kwargs)
        else:
            raise ValueError(f"Unknown operation: {operation}")
