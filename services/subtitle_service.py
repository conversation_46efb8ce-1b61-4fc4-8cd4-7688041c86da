"""Service for handling subtitle-related operations."""

import logging
from typing import Any

from models.schemas import (
    SubtitleRequest,
    SubtitleResponse,
    TaskStatus,
    VideoListResponse,
)
from services.base import BaseService
from worker.queue import TaskQueue

logger = logging.getLogger(__name__)


class SubtitleService(BaseService):
    """Service for handling subtitle-related operations."""

    def __init__(self, task_queue: TaskQueue):
        """
        Initialize the subtitle service.

        Args:
            task_queue: The task queue for managing background tasks.
        """
        super().__init__()
        self.task_queue = task_queue

    async def get_video_ids(self, request: SubtitleRequest) -> VideoListResponse:
        """
        Get video IDs from a YouTube URL.

        Args:
            request: The request containing the YouTube URL.

        Returns:
            VideoListResponse: The response containing the task ID and status.
        """
        # Note: client_uid is deprecated and ignored in processing
        if request.client_uid:
            self.logger.debug(
                f"Received deprecated client_uid: {request.client_uid} (ignored for processing)"
            )

        self.logger.debug(f"Getting video IDs for URL: {request.url}")

        try:
            # Add task to queue
            response = await self.task_queue.add_video_list_task(
                str(request.url), client_uid=None  # Explicitly set to None - deprecated field
            )

            self.logger.debug(
                f"Video list task added. Response: ID {response.task_id}, "
                f"Status {response.status}"
            )

            return response

        except Exception as e:
            self.logger.error(f"Error getting video IDs: {e}", exc_info=True)
            raise

    async def create_subtitles_task(self, request: SubtitleRequest) -> SubtitleResponse:
        """
        Create a new subtitle extraction task.

        Args:
            request: The request containing the YouTube URL.

        Returns:
            SubtitleResponse: The response containing the task ID and status.
        """
        # Note: client_uid is deprecated and ignored in processing
        if request.client_uid:
            self.logger.debug(
                f"Received deprecated client_uid: {request.client_uid} (ignored for processing)"
            )

        self.logger.debug(f"Creating subtitle task for URL: {request.url}")

        try:
            # Add task to queue
            response = await self.task_queue.add_subtitle_task(
                str(request.url), client_uid=None  # Explicitly set to None - deprecated field
            )

            self.logger.debug(
                f"Subtitle task added. Response: ID {response.task_id}, "
                f"Status {response.status}"
            )

            return response

        except Exception as e:
            self.logger.error(f"Error creating subtitle task: {e}", exc_info=True)
            raise

    async def get_task_status(self, task_id: str) -> dict[str, Any]:
        """
        Get the status of a subtitle task.

        Args:
            task_id: The ID of the task to check.

        Returns:
            Dict containing the task status and result if available.
        """
        self.logger.debug(f"Getting status for task: {task_id}")

        try:
            task_status = await self.task_queue.get_task_status(task_id)

            if task_status is None:
                return {
                    "status": TaskStatus.NOT_FOUND,
                    "error": f"Task {task_id} not found",
                }

            return {
                "status": task_status.status,
                "progress": task_status.progress,
                "result": task_status.result,
                "error": task_status.error,
                "created_at": (
                    task_status.created_at.isoformat()
                    if task_status.created_at
                    else None
                ),
                "updated_at": (
                    task_status.updated_at.isoformat()
                    if task_status.updated_at
                    else None
                ),
                "completed_at": (
                    task_status.completed_at.isoformat()
                    if task_status.completed_at
                    else None
                ),
            }

        except Exception as e:
            self.logger.error(
                f"Error getting task status for {task_id}: {e}", exc_info=True
            )
            return {
                "status": TaskStatus.FAILED,
                "error": f"Error getting task status: {str(e)}",
            }

    async def execute(self, *args: Any, **kwargs: Any) -> Any:
        """
        Execute the service operation.

        This is a generic method that routes to the appropriate method based on the operation.

        Args:
            *args: Positional arguments for the service.
            **kwargs: Keyword arguments for the service.

        Returns:
            The result of the service operation.
        """
        operation = kwargs.pop("operation", None)

        if operation == "get_video_ids":
            return await self.get_video_ids(*args, **kwargs)
        elif operation == "create_subtitles_task":
            return await self.create_subtitles_task(*args, **kwargs)
        elif operation == "get_task_status":
            return await self.get_task_status(*args, **kwargs)
        else:
            raise ValueError(f"Unknown operation: {operation}")
