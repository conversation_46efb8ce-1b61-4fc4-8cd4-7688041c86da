{"tests/unit/test_config.py::TestSettings::test_default_settings": true, "tests/unit/test_utils.py::TestProxyManager::test_setup_proxy_connection_error": true, "tests/integration/test_api.py::TestHealthEndpoints::test_health_endpoint": true, "tests/integration/test_api.py::TestHealthEndpoints::test_metrics_endpoint": true, "tests/unit/test_config.py::TestSettings::test_logging_settings": true, "tests/unit/test_config.py::TestSettings::test_cors_settings": true, "tests/unit/test_config.py::TestSettings::test_auth_settings": true, "tests/unit/test_config.py::TestSettings::test_endpoint_specific_rate_limits": true, "tests/unit/test_config.py::TestSettings::test_database_settings": true, "tests/unit/test_config.py::TestSettings::test_external_api_settings": true, "tests/unit/test_config.py::TestGetSettings::test_get_settings_with_env_override": true, "tests/unit/test_models.py::TestMessageType::test_message_type_values": true, "tests/unit/test_models.py::TestSubtitleRequest::test_subtitle_request_invalid_url": true, "tests/unit/test_models.py::TestSummarizeRequest::test_summarize_request_text_too_short": true, "tests/unit/test_models.py::TestSummarizeRequest::test_summarize_request_text_too_long": true, "tests/unit/test_models.py::TestSummarizeRequest::test_summarize_request_empty_text": true, "tests/unit/test_models.py::TestWebSocketMessage::test_valid_websocket_message": true, "tests/unit/test_models.py::TestWebSocketMessage::test_websocket_subtitle_message": true, "tests/unit/test_models.py::TestWebSocketMessage::test_websocket_summarize_request": true, "tests/unit/test_models.py::TestAPIKeyRequest::test_api_key_request_empty_permissions": true, "tests/unit/test_models.py::TestAPIKeyRequest::test_api_key_request_invalid_permissions": true, "tests/unit/test_services.py::TestConverterService::test_ttml_to_txt_conversion_valid": true, "tests/unit/test_services.py::TestConverterService::test_ttml_to_txt_conversion_empty": true, "tests/unit/test_services.py::TestConverterService::test_ttml_to_txt_conversion_invalid_xml": true, "tests/unit/test_services.py::TestConverterService::test_ttml_to_txt_conversion_no_subtitles": true, "tests/unit/test_services.py::TestConverterService::test_ttml_to_txt_conversion_malformed_ttml": true, "tests/unit/test_services.py::TestConverterService::test_ttml_to_txt_conversion_multiple_paragraphs": true, "tests/unit/test_services.py::TestConverterService::test_ttml_to_txt_conversion_with_formatting": true, "tests/unit/test_services.py::TestSummarizeService::test_create_summarize_task": true, "tests/unit/test_services.py::TestSummarizeService::test_create_summarize_file_task": true, "tests/unit/test_services.py::TestTaskService::test_get_task_status": true, "tests/unit/test_services.py::TestTaskService::test_get_task_status_not_found": true, "tests/unit/test_services.py::TestTaskService::test_task_service_error_handling": true, "tests/unit/test_services.py::TestTaskService::test_cancel_task": true, "tests/unit/test_services.py::TestServiceIntegration::test_subtitle_to_summarize_workflow": true, "tests/unit/test_models.py::TestFileUploadRequest::test_valid_file_upload_request": true, "tests/unit/test_models.py::TestFileUploadRequest::test_file_upload_request_invalid_filename": true, "tests/unit/test_models.py::TestFileUploadRequest::test_file_upload_request_invalid_base64": true, "tests/integration/test_api.py::TestSubtitleEndpoints::test_subtitles_with_client_uid_deprecated": true}