["tests/integration/test_api.py::TestHealthEndpoints::test_health_endpoint", "tests/integration/test_api.py::TestHealthEndpoints::test_metrics_endpoint", "tests/integration/test_api.py::TestHealthEndpoints::test_ping_endpoint", "tests/integration/test_api.py::TestSubtitleEndpoints::test_subtitles_with_client_uid_deprecated", "tests/unit/test_config.py::TestConfigValidation::test_cors_headers_validation", "tests/unit/test_config.py::TestConfigValidation::test_cors_methods_validation", "tests/unit/test_config.py::TestConfigValidation::test_database_type_validation", "tests/unit/test_config.py::TestConfigValidation::test_log_level_validation", "tests/unit/test_config.py::TestGetSettings::test_get_settings_singleton", "tests/unit/test_config.py::TestGetSettings::test_get_settings_type", "tests/unit/test_config.py::TestGetSettings::test_get_settings_with_env_override", "tests/unit/test_config.py::TestSettings::test_auth_settings", "tests/unit/test_config.py::TestSettings::test_cors_settings", "tests/unit/test_config.py::TestSettings::test_database_settings", "tests/unit/test_config.py::TestSettings::test_default_settings", "tests/unit/test_config.py::TestSettings::test_endpoint_specific_rate_limits", "tests/unit/test_config.py::TestSettings::test_external_api_settings", "tests/unit/test_config.py::TestSettings::test_logging_settings", "tests/unit/test_config.py::TestSettings::test_rate_limiting_settings", "tests/unit/test_config.py::TestSettings::test_task_queue_settings", "tests/unit/test_config.py::TestSettings::test_websocket_settings", "tests/unit/test_config.py::TestSettingsFromEnvironment::test_boolean_env_vars", "tests/unit/test_config.py::TestSettingsFromEnvironment::test_cors_origins_parsing", "tests/unit/test_config.py::TestSettingsFromEnvironment::test_integer_env_vars", "tests/unit/test_config.py::TestSettingsFromEnvironment::test_invalid_env_vars", "tests/unit/test_config.py::TestSettingsFromEnvironment::test_prometheus_settings", "tests/unit/test_config.py::TestSettingsFromEnvironment::test_rate_limit_overrides", "tests/unit/test_config.py::TestSettingsFromEnvironment::test_security_headers_settings", "tests/unit/test_config.py::TestSettingsFromEnvironment::test_settings_from_env_vars", "tests/unit/test_models.py::TestAPIKeyRequest::test_api_key_request_admin_permissions", "tests/unit/test_models.py::TestAPIKeyRequest::test_api_key_request_empty_permissions", "tests/unit/test_models.py::TestAPIKeyRequest::test_api_key_request_invalid_name", "tests/unit/test_models.py::TestAPIKeyRequest::test_api_key_request_invalid_permissions", "tests/unit/test_models.py::TestAPIKeyRequest::test_valid_api_key_request", "tests/unit/test_models.py::TestFileUploadRequest::test_file_upload_request_invalid_base64", "tests/unit/test_models.py::TestFileUploadRequest::test_file_upload_request_invalid_filename", "tests/unit/test_models.py::TestFileUploadRequest::test_valid_file_upload_request", "tests/unit/test_models.py::TestMessageType::test_message_type_values", "tests/unit/test_models.py::TestSubtitleRequest::test_subtitle_request_invalid_client_uid", "tests/unit/test_models.py::TestSubtitleRequest::test_subtitle_request_invalid_url", "tests/unit/test_models.py::TestSubtitleRequest::test_subtitle_request_without_client_uid", "tests/unit/test_models.py::TestSubtitleRequest::test_valid_subtitle_request", "tests/unit/test_models.py::TestSummarizeRequest::test_summarize_request_empty_text", "tests/unit/test_models.py::TestSummarizeRequest::test_summarize_request_text_too_long", "tests/unit/test_models.py::TestSummarizeRequest::test_summarize_request_text_too_short", "tests/unit/test_models.py::TestSummarizeRequest::test_summarize_request_without_optional_fields", "tests/unit/test_models.py::TestSummarizeRequest::test_valid_summarize_request", "tests/unit/test_models.py::TestTaskStatus::test_task_status_values", "tests/unit/test_models.py::TestValidators::test_validate_client_uid_invalid", "tests/unit/test_models.py::TestValidators::test_validate_client_uid_none", "tests/unit/test_models.py::TestValidators::test_validate_client_uid_valid", "tests/unit/test_models.py::TestValidators::test_validate_filename_invalid", "tests/unit/test_models.py::TestValidators::test_validate_filename_valid", "tests/unit/test_models.py::TestValidators::test_validate_youtube_url_invalid", "tests/unit/test_models.py::TestValidators::test_validate_youtube_url_valid", "tests/unit/test_models.py::TestWebSocketMessage::test_valid_websocket_message", "tests/unit/test_models.py::TestWebSocketMessage::test_websocket_subtitle_message", "tests/unit/test_models.py::TestWebSocketMessage::test_websocket_summarize_request", "tests/unit/test_services.py::TestConverterService::test_converter_service_creation", "tests/unit/test_services.py::TestConverterService::test_ttml_to_txt_conversion_empty", "tests/unit/test_services.py::TestConverterService::test_ttml_to_txt_conversion_invalid_xml", "tests/unit/test_services.py::TestConverterService::test_ttml_to_txt_conversion_malformed_ttml", "tests/unit/test_services.py::TestConverterService::test_ttml_to_txt_conversion_multiple_paragraphs", "tests/unit/test_services.py::TestConverterService::test_ttml_to_txt_conversion_no_subtitles", "tests/unit/test_services.py::TestConverterService::test_ttml_to_txt_conversion_valid", "tests/unit/test_services.py::TestConverterService::test_ttml_to_txt_conversion_with_formatting", "tests/unit/test_services.py::TestServiceConfiguration::test_converter_service_configuration", "tests/unit/test_services.py::TestServiceConfiguration::test_service_logging_configuration", "tests/unit/test_services.py::TestServiceConfiguration::test_service_timeout_configuration", "tests/unit/test_services.py::TestServiceIntegration::test_service_dependency_injection", "tests/unit/test_services.py::TestServiceIntegration::test_service_error_propagation", "tests/unit/test_services.py::TestServiceIntegration::test_service_factory_pattern", "tests/unit/test_services.py::TestServiceIntegration::test_subtitle_to_summarize_workflow", "tests/unit/test_services.py::TestSubtitleService::test_create_subtitles_task", "tests/unit/test_services.py::TestSubtitleService::test_get_video_ids", "tests/unit/test_services.py::TestSubtitleService::test_subtitle_service_creation", "tests/unit/test_services.py::TestSubtitleService::test_subtitle_service_error_handling", "tests/unit/test_services.py::TestSummarizeService::test_create_summarize_file_task", "tests/unit/test_services.py::TestSummarizeService::test_create_summarize_task", "tests/unit/test_services.py::TestSummarizeService::test_summarize_service_creation", "tests/unit/test_services.py::TestSummarizeService::test_summarize_service_error_handling", "tests/unit/test_services.py::TestTaskService::test_cancel_task", "tests/unit/test_services.py::TestTaskService::test_get_task_status", "tests/unit/test_services.py::TestTaskService::test_get_task_status_not_found", "tests/unit/test_services.py::TestTaskService::test_list_tasks", "tests/unit/test_services.py::TestTaskService::test_task_service_creation", "tests/unit/test_services.py::TestTaskService::test_task_service_error_handling", "tests/unit/test_utils.py::TestInterceptHandler::test_intercept_handler_creation", "tests/unit/test_utils.py::TestInterceptHandler::test_intercept_handler_emit", "tests/unit/test_utils.py::TestLoggingConfiguration::test_json_log_format_config", "tests/unit/test_utils.py::TestLoggingConfiguration::test_log_format_config", "tests/unit/test_utils.py::TestLoggingConfiguration::test_log_retention_config", "tests/unit/test_utils.py::TestLoggingConfiguration::test_log_rotation_config", "tests/unit/test_utils.py::TestLoggingFilters::test_filter_noisy_logs", "tests/unit/test_utils.py::TestLoggingFilters::test_uvicorn_access_log_filtering", "tests/unit/test_utils.py::TestLoggingSetup::test_json_format", "tests/unit/test_utils.py::TestLoggingSetup::test_log_file_creation", "tests/unit/test_utils.py::TestLoggingSetup::test_log_levels", "tests/unit/test_utils.py::TestLoggingSetup::test_setup_logging_loguru", "tests/unit/test_utils.py::TestLoggingSetup::test_setup_logging_structured", "tests/unit/test_utils.py::TestLoggingSetup::test_setup_loguru_logging_default", "tests/unit/test_utils.py::TestLoggingSetup::test_setup_loguru_logging_with_params", "tests/unit/test_utils.py::TestLoggingSetup::test_setup_structured_logging", "tests/unit/test_utils.py::TestProxyManager::test_get_yt_dlp_proxy_connected", "tests/unit/test_utils.py::TestProxyManager::test_get_yt_dlp_proxy_not_connected", "tests/unit/test_utils.py::TestProxyManager::test_is_connected_property", "tests/unit/test_utils.py::TestProxyManager::test_proxy_manager_creation", "tests/unit/test_utils.py::TestProxyManager::test_proxy_manager_no_proxy", "tests/unit/test_utils.py::TestProxyManager::test_proxy_manager_with_proxy_env", "tests/unit/test_utils.py::TestProxyManager::test_setup_proxy_connection_error", "tests/unit/test_utils.py::TestProxyManager::test_setup_proxy_invalid_url_format", "tests/unit/test_utils.py::TestProxyManager::test_setup_proxy_no_proxy", "tests/unit/test_utils.py::TestProxyManager::test_setup_proxy_valid_url"]