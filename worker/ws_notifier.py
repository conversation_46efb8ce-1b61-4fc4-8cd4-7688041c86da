import asyncio
from typing import Any

# from fastapi import WebSocket
from loguru import logger

from models.schemas import (
    MessageType,
    TaskStatus,
    WebSocketMessage,
    WebSocketSubtitleMessage,
    WebSocketSummarizeMessage,
)


class WebSocketNotifier:
    """Класс для управления WebSocket уведомлениями"""

    def __init__(self, ws_manager: Any):
        self.ws_manager = ws_manager

    async def notify_subscribers_of_cancellation(
        self, task_id: str, error_message: str, tasks: dict[str, Any]
    ):
        """Notify WebSocket subscribers that a task has been cancelled."""
        logger.debug(
            f"Attempting to notify subscribers of cancellation for task_id: {task_id}, message: '{error_message}'"
        )
        task_info = tasks.get(task_id)
        if not task_info:
            logger.debug(
                f"Task info not found for {task_id} during cancellation notification. Cannot determine client_uid or message type."
            )
            return

        client_uid = task_info.client_uid if hasattr(task_info, "client_uid") else None

        # Determine message type based on task_id prefix or task type
        # This is a heuristic; ideally, task_info would explicitly state its type
        message_model = (
            WebSocketSummarizeMessage  # Default, or determine more accurately
        )
        # Crude check for subtitle/video_list
        if "list_" in task_id or hasattr(task_info, "en_subtitles"):
            message_model = WebSocketSubtitleMessage

        message = message_model(
            type=MessageType.STATUS,
            task_id=task_id,
            status=TaskStatus.CANCELLED,
            error=error_message,
            client_uid=client_uid,
        )
        await self.notify_subscribers(task_id, message)

    async def notify_subscribers(self, task_id: str, message: WebSocketMessage):
        """Send update to all WebSocket subscribers of a task with retry logic"""
        max_retries = 3
        retry_delay = 1.0  # секунды

        for attempt in range(max_retries):
            try:
                await self.ws_manager.broadcast_to_task(task_id, message.model_dump())
                break
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(
                        f"Failed to notify subscribers after {max_retries} attempts: {e}"
                    )
                else:
                    logger.warning(f"Retry {attempt + 1}/{max_retries} failed: {e}")
                    await asyncio.sleep(retry_delay)

    async def close_connections_for_task(self, task_id: str, error_message: str):
        """Закрываем WebSocket соединения для задачи с ошибкой"""
        subscribers = self.ws_manager.task_subscribers.get(task_id, set())
        for websocket in subscribers:
            try:
                await websocket.close(code=1011, reason=error_message)
                logger.debug(f"Closed WebSocket connection for failed task {task_id}")
            except Exception as close_error:
                logger.error(
                    f"Error closing WebSocket for task {task_id}: {close_error}",
                    exc_info=True,
                )

    async def notify_task_update(self, task_id: str, task: Any) -> None:
        """Send a task status update to all WebSocket subscribers.

        Args:
            task_id: The ID of the task to update
            task: The task object containing status and other details
        """
        try:
            # Determine the appropriate message type based on the task type
            if hasattr(task, "en_subtitles") or hasattr(task, "ru_subtitles"):
                # This is a subtitle task
                message = WebSocketSubtitleMessage(
                    type=MessageType.STATUS,
                    task_id=task_id,
                    status=task.status,
                    error=getattr(task, "error", None),
                    client_uid=getattr(task, "client_uid", None),
                    title=getattr(task, "title", None),
                    original_language=getattr(task, "original_language", None),
                    publish_date=getattr(task, "publish_date", None),
                    en_subtitles=getattr(task, "en_subtitles", None),
                    ru_subtitles=getattr(task, "ru_subtitles", None),
                )
            elif hasattr(task, "summary"):
                # This is a summarization task
                message = WebSocketSummarizeMessage(
                    type=MessageType.STATUS,
                    task_id=task_id,
                    status=task.status,
                    error=getattr(task, "error", None),
                    client_uid=getattr(task, "client_uid", None),
                    summary=getattr(task, "summary", None),
                )
            else:
                # Generic task update
                message = WebSocketMessage(
                    type=MessageType.STATUS,
                    task_id=task_id,
                    status=task.status,
                    error=getattr(task, "error", None),
                    client_uid=getattr(task, "client_uid", None),
                )

            await self.notify_subscribers(task_id, message)

        except Exception as e:
            logger.error(
                f"Error in notify_task_update for task {task_id}: {e}", exc_info=True
            )
