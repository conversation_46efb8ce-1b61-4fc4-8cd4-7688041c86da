"""
Integration tests for API endpoints.
"""

from fastapi import status
from fastapi.testclient import TestClient

from models.schemas import TaskStatus


class TestHealthEndpoints:
    """Test health check endpoints."""

    def test_health_endpoint(self, client: TestClient):
        """Test /health endpoint."""
        response = client.get("/health")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Check response structure
        assert "status" in data
        assert "timestamp" in data
        assert "version" in data
        assert "uptime" in data

        # Check status
        assert data["status"] == "healthy"

    def test_ping_endpoint(self, client: TestClient):
        """Test /ping endpoint."""
        response = client.post("/ping")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        assert data["message"] == "pong"

    def test_metrics_endpoint(self, client: TestClient):
        """Test /metrics endpoint (if Prometheus is enabled)."""
        response = client.get("/metrics")

        # Metrics endpoint might not be available in test mode
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_404_NOT_FOUND]


class TestSubtitleEndpoints:
    """Test subtitle-related endpoints."""

    def test_create_subtitles_task_without_auth(
        self, client: TestClient, sample_youtube_urls
    ):
        """Test creating subtitle task without authentication."""
        response = client.post(
            "/api/subtitles", json={"url": sample_youtube_urls["valid"][0]}
        )

        # Should work if auth is disabled in test settings
        # Or return 401 if auth is required
        assert response.status_code in [
            status.HTTP_202_ACCEPTED,
            status.HTTP_401_UNAUTHORIZED,
        ]

    def test_create_subtitles_task_with_auth(
        self, client: TestClient, sample_youtube_urls, auth_headers
    ):
        """Test creating subtitle task with authentication."""
        response = client.post(
            "/api/subtitles",
            json={"url": sample_youtube_urls["valid"][0]},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_202_ACCEPTED
        data = response.json()

        # Check response structure
        assert "task_id" in data
        assert "status" in data
        assert data["status"] in [
            TaskStatus.PENDING,
            TaskStatus.PROCESSING,
            TaskStatus.COMPLETED,
        ]

    def test_create_subtitles_task_invalid_url(
        self, client: TestClient, sample_youtube_urls, auth_headers
    ):
        """Test creating subtitle task with invalid URL."""
        response = client.post(
            "/api/subtitles",
            json={"url": sample_youtube_urls["invalid"][0]},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()

        assert "error" in data or "detail" in data

    def test_get_video_ids(self, client: TestClient, sample_youtube_urls, auth_headers):
        """Test getting video IDs from URL."""
        response = client.post(
            "/api/subtitles/get_id",
            json={"url": sample_youtube_urls["valid"][0]},
            headers=auth_headers,
        )

        assert response.status_code in [status.HTTP_200_OK, status.HTTP_202_ACCEPTED]
        data = response.json()

        # Check response structure
        assert "task_id" in data
        assert "status" in data

    def test_subtitles_with_client_uid_deprecated(
        self, client: TestClient, sample_youtube_urls, auth_headers
    ):
        """Test subtitle request with deprecated client_uid field."""
        response = client.post(
            "/api/subtitles",
            json={
                "url": sample_youtube_urls["valid"][0],
                "client_uid": "deprecated_client_123",
            },
            headers=auth_headers,
        )

        # Should still work but client_uid should be ignored
        assert response.status_code == status.HTTP_202_ACCEPTED


class TestSummarizeEndpoints:
    """Test summarization endpoints."""

    def test_create_summarize_task(
        self, client: TestClient, sample_text_content, auth_headers
    ):
        """Test creating summarization task."""
        response = client.post(
            "/api/summarize",
            json={"og_text": sample_text_content["medium"]},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_202_ACCEPTED
        data = response.json()

        # Check response structure
        assert "task_id" in data
        assert "status" in data
        assert data["status"] in [
            TaskStatus.PENDING,
            TaskStatus.PROCESSING,
            TaskStatus.COMPLETED,
        ]

    def test_create_summarize_task_with_mode(
        self, client: TestClient, sample_text_content, auth_headers
    ):
        """Test creating summarization task with specific mode."""
        response = client.post(
            "/api/summarize",
            json={"og_text": sample_text_content["medium"], "mode": "default"},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_202_ACCEPTED

    def test_create_summarize_task_text_too_short(
        self, client: TestClient, sample_text_content, auth_headers
    ):
        """Test creating summarization task with text too short."""
        response = client.post(
            "/api/summarize",
            json={"og_text": sample_text_content["short"]},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_create_summarize_task_text_too_long(
        self, client: TestClient, sample_text_content, auth_headers
    ):
        """Test creating summarization task with text too long."""
        response = client.post(
            "/api/summarize",
            json={"og_text": sample_text_content["long"]},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_summarize_file_upload(
        self, client: TestClient, sample_file_uploads, auth_headers
    ):
        """Test file upload for summarization."""
        file_data = sample_file_uploads["text_file"]

        # Create multipart form data
        files = {
            "file": (
                file_data["filename"],
                file_data["content"],
                file_data["mime_type"],
            )
        }

        response = client.post("/api/summarize/file", files=files, headers=auth_headers)

        # Note: This might fail if the endpoint expects actual file upload
        # rather than base64 content
        assert response.status_code in [
            status.HTTP_202_ACCEPTED,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_400_BAD_REQUEST,
        ]


class TestTaskEndpoints:
    """Test task management endpoints."""

    def test_get_task_status_nonexistent(self, client: TestClient, auth_headers):
        """Test getting status of non-existent task."""
        response = client.get("/api/task/nonexistent_task_id", headers=auth_headers)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_get_task_status_invalid_id(self, client: TestClient, auth_headers):
        """Test getting status with invalid task ID format."""
        response = client.get("/api/task/invalid-id-format", headers=auth_headers)

        assert response.status_code in [
            status.HTTP_404_NOT_FOUND,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        ]


class TestConverterEndpoints:
    """Test converter endpoints."""

    def test_convert_ttml_to_txt(self, client: TestClient, sample_ttml_content):
        """Test TTML to TXT conversion."""
        response = client.post(
            "/api/convert/ttml-to-txt", json={"ttml_content": sample_ttml_content}
        )

        # This endpoint might not require auth based on DISABLE_AUTH_FOR_TTML_CONVERSION
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        ]

        if response.status_code == status.HTTP_200_OK:
            data = response.json()
            assert "txt_content" in data

    def test_convert_ttml_to_txt_invalid_content(self, client: TestClient):
        """Test TTML to TXT conversion with invalid content."""
        response = client.post(
            "/api/convert/ttml-to-txt", json={"ttml_content": "invalid ttml content"}
        )

        # Should return error for invalid TTML
        assert response.status_code in [
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_401_UNAUTHORIZED,  # If auth is required
        ]

    def test_convert_ttml_to_txt_empty_content(self, client: TestClient):
        """Test TTML to TXT conversion with empty content."""
        response = client.post("/api/convert/ttml-to-txt", json={"ttml_content": ""})

        assert response.status_code in [
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_401_UNAUTHORIZED,
        ]


class TestAuthEndpoints:
    """Test authentication endpoints."""

    def test_get_current_user_without_auth(self, client: TestClient):
        """Test getting current user info without authentication."""
        response = client.get("/api/auth/me")

        # Should require authentication
        assert response.status_code in [
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_200_OK,  # If auth is disabled in tests
        ]

    def test_get_current_user_with_auth(self, client: TestClient, auth_headers):
        """Test getting current user info with authentication."""
        response = client.get("/api/auth/me", headers=auth_headers)

        # Should work if auth is properly configured
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_401_UNAUTHORIZED,  # If mock auth is not working
        ]

        if response.status_code == status.HTTP_200_OK:
            data = response.json()
            assert "name" in data
            assert "permissions" in data
            assert "rate_limit" in data
            assert "api_key_preview" in data

    def test_create_api_key_without_admin(
        self, client: TestClient, auth_headers, sample_api_keys
    ):
        """Test creating API key without admin permissions."""
        response = client.post(
            "/api/auth/keys", json=sample_api_keys["valid"], headers=auth_headers
        )

        # Should require admin permissions
        assert response.status_code in [
            status.HTTP_403_FORBIDDEN,
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_201_CREATED,  # If test user has admin permissions
        ]

    def test_revoke_api_key_without_admin(self, client: TestClient, auth_headers):
        """Test revoking API key without admin permissions."""
        response = client.delete(
            "/api/auth/keys/test_key_preview", headers=auth_headers
        )

        # Should require admin permissions
        assert (
            response.status_code
            in [
                status.HTTP_403_FORBIDDEN,
                status.HTTP_401_UNAUTHORIZED,
                status.HTTP_404_NOT_FOUND,  # If test user has admin permissions but key not found
            ]
        )


class TestErrorHandling:
    """Test error handling in API endpoints."""

    def test_invalid_json_request(self, client: TestClient, auth_headers):
        """Test request with invalid JSON."""
        response = client.post(
            "/api/subtitles",
            data="invalid json",
            headers={**auth_headers, "Content-Type": "application/json"},
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_missing_required_fields(self, client: TestClient, auth_headers):
        """Test request with missing required fields."""
        response = client.post(
            "/api/subtitles",
            json={},  # Missing required 'url' field
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "error" in data or "detail" in data

    def test_invalid_content_type(
        self, client: TestClient, auth_headers, sample_youtube_urls
    ):
        """Test request with invalid content type."""
        response = client.post(
            "/api/subtitles",
            data=f"url={sample_youtube_urls['valid'][0]}",
            headers={
                **auth_headers,
                "Content-Type": "application/x-www-form-urlencoded",
            },
        )

        # FastAPI should handle this gracefully
        assert response.status_code in [
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
        ]

    def test_method_not_allowed(self, client: TestClient):
        """Test using wrong HTTP method."""
        response = client.get("/api/subtitles")  # Should be POST

        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED

    def test_endpoint_not_found(self, client: TestClient):
        """Test accessing non-existent endpoint."""
        response = client.get("/api/nonexistent")

        assert response.status_code == status.HTTP_404_NOT_FOUND


class TestRateLimiting:
    """Test rate limiting functionality."""

    def test_rate_limiting_disabled_in_tests(
        self, client: TestClient, sample_youtube_urls, auth_headers
    ):
        """Test that rate limiting is disabled in test environment."""
        # Make multiple rapid requests
        for _ in range(5):
            response = client.post(
                "/api/subtitles",
                json={"url": sample_youtube_urls["valid"][0]},
                headers=auth_headers,
            )

            # Should not be rate limited in tests
            assert response.status_code != status.HTTP_429_TOO_MANY_REQUESTS
