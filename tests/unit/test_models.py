"""
Unit tests for data models and validation.
"""

import pytest
from pydantic import ValidationError

from models.auth_schemas import APIKeyRequest, Permission
from models.file_schemas import FileUploadRequest
from models.schemas import (
    MessageType,
    SubtitleRequest,
    SummarizeMode,
    SummarizeRequest,
    TaskStatus,
    WebSocketMessage,
    WebSocketSubtitleMessage,
    WebSocketSummarizeRequest,
)
from models.validators import (
    validate_client_uid,
    validate_filename,
    validate_youtube_url,
)


class TestValidators:
    """Test custom validators."""

    def test_validate_youtube_url_valid(self, sample_youtube_urls):
        """Test YouTube URL validation with valid URLs."""
        for url in sample_youtube_urls["valid"]:
            result = validate_youtube_url(url)
            assert result == url

    def test_validate_youtube_url_invalid(self, sample_youtube_urls):
        """Test YouTube URL validation with invalid URLs."""
        for url in sample_youtube_urls["invalid"]:
            with pytest.raises(ValueError):
                validate_youtube_url(url)

    def test_validate_client_uid_valid(self):
        """Test client UID validation with valid UIDs."""
        valid_uids = ["user_123", "app-mobile-v2", "test_user_001", "ABC123"]
        for uid in valid_uids:
            result = validate_client_uid(uid)
            assert result == uid

    def test_validate_client_uid_invalid(self):
        """Test client UID validation with invalid UIDs."""
        invalid_uids = [
            "ab",  # Too short
            "user@invalid",  # Invalid characters
            "user with spaces",  # Spaces
            "a" * 101,  # Too long
        ]
        for uid in invalid_uids:
            with pytest.raises(ValueError):
                validate_client_uid(uid)

    def test_validate_client_uid_none(self):
        """Test client UID validation with None value."""
        result = validate_client_uid(None)
        assert result is None

    def test_validate_filename_valid(self):
        """Test filename validation with valid filenames."""
        valid_filenames = ["document.txt", "my-file_v2.pdf", "report.docx", "notes.md"]
        for filename in valid_filenames:
            result = validate_filename(filename)
            assert result == filename

    def test_validate_filename_invalid(self):
        """Test filename validation with invalid filenames."""
        invalid_filenames = [
            "../../../etc/passwd",  # Path traversal
            "file<script>.txt",  # HTML characters
            "file|pipe.txt",  # Reserved characters
            "a" * 300 + ".txt",  # Too long
        ]
        for filename in invalid_filenames:
            with pytest.raises(ValueError):
                validate_filename(filename)


class TestTaskStatus:
    """Test TaskStatus enum."""

    def test_task_status_values(self):
        """Test TaskStatus enum values."""
        assert TaskStatus.PENDING == "pending"
        assert TaskStatus.PROCESSING == "processing"
        assert TaskStatus.COMPLETED == "completed"
        assert TaskStatus.FAILED == "failed"
        assert TaskStatus.CANCELLED == "cancelled"


class TestMessageType:
    """Test MessageType enum."""

    def test_message_type_values(self):
        """Test MessageType enum values."""
        assert MessageType.STATUS_UPDATE == "status_update"
        assert MessageType.TEXT_INPUT == "text_input"
        assert MessageType.FILE_INPUT == "file_input"


class TestSubtitleRequest:
    """Test SubtitleRequest model."""

    def test_valid_subtitle_request(self, sample_youtube_urls):
        """Test valid subtitle request creation."""
        request = SubtitleRequest(
            url=sample_youtube_urls["valid"][0],
            client_uid="test_user_123",
        )
        assert request.url == sample_youtube_urls["valid"][0]
        assert request.client_uid == "test_user_123"

    def test_subtitle_request_without_client_uid(self, sample_youtube_urls):
        """Test subtitle request without client_uid."""
        request = SubtitleRequest(url=sample_youtube_urls["valid"][0])
        assert request.url == sample_youtube_urls["valid"][0]
        assert request.client_uid is None

    def test_subtitle_request_invalid_url(self, sample_youtube_urls):
        """Test subtitle request with invalid URL."""
        with pytest.raises(ValidationError) as exc_info:
            SubtitleRequest(url=sample_youtube_urls["invalid"][0])

        errors = exc_info.value.errors()
        assert any("Invalid YouTube URL" in str(error["msg"]) for error in errors)

    def test_subtitle_request_invalid_client_uid(self, sample_youtube_urls):
        """Test subtitle request with invalid client_uid."""
        with pytest.raises(ValidationError) as exc_info:
            SubtitleRequest(
                url=sample_youtube_urls["valid"][0], client_uid="user@invalid"
            )

        errors = exc_info.value.errors()
        assert any("client_uid" in str(error["loc"]) for error in errors)


class TestSummarizeRequest:
    """Test SummarizeRequest model."""

    def test_valid_summarize_request(self, sample_text_content):
        """Test valid summarize request creation."""
        request = SummarizeRequest(
            og_text=sample_text_content["medium"],
            mode=SummarizeMode.DEFAULT,
            client_uid="test_user",
        )
        assert request.og_text == sample_text_content["medium"]
        assert request.mode == SummarizeMode.DEFAULT
        assert request.client_uid == "test_user"

    def test_summarize_request_without_optional_fields(self, sample_text_content):
        """Test summarize request without optional fields."""
        request = SummarizeRequest(og_text=sample_text_content["medium"])
        assert request.og_text == sample_text_content["medium"]
        assert request.mode is None
        assert request.client_uid is None

    def test_summarize_request_text_too_short(self, sample_text_content):
        """Test summarize request with text too short."""
        with pytest.raises(ValidationError) as exc_info:
            SummarizeRequest(og_text=sample_text_content["short"])

        errors = exc_info.value.errors()
        assert any("at least 50 characters" in str(error["msg"]) for error in errors)

    def test_summarize_request_text_too_long(self, sample_text_content):
        """Test summarize request with text too long."""
        with pytest.raises(ValidationError) as exc_info:
            SummarizeRequest(og_text=sample_text_content["long"])

        errors = exc_info.value.errors()
        assert any("exceed 500KB" in str(error["msg"]) for error in errors)

    def test_summarize_request_empty_text(self, sample_text_content):
        """Test summarize request with empty text."""
        with pytest.raises(ValidationError) as exc_info:
            SummarizeRequest(og_text=sample_text_content["empty"])

        errors = exc_info.value.errors()
        assert any("at least 1 character" in str(error["msg"]) for error in errors)


class TestWebSocketMessage:
    """Test WebSocket message models."""

    def test_valid_websocket_message(self):
        """Test valid WebSocket message creation."""
        message = WebSocketMessage(
            type=MessageType.STATUS_UPDATE,
            status=TaskStatus.COMPLETED,
            task_id="test_task_123",
            client_uid="test_user",
        )
        assert message.type == MessageType.STATUS_UPDATE
        assert message.status == TaskStatus.COMPLETED
        assert message.task_id == "test_task_123"
        assert message.client_uid == "test_user"

    def test_websocket_subtitle_message(self):
        """Test WebSocket subtitle message."""
        message = WebSocketSubtitleMessage(
            type=MessageType.STATUS_UPDATE,
            status=TaskStatus.COMPLETED,
            task_id="test_task_123",
            title="Test Video",
            original_language="en",
            en_subtitles="English subtitles",
            ru_subtitles="Russian subtitles",
        )
        assert message.title == "Test Video"
        assert message.original_language == "en"
        assert message.en_subtitles == "English subtitles"
        assert message.ru_subtitles == "Russian subtitles"

    def test_websocket_summarize_request(self, sample_text_content):
        """Test WebSocket summarize request."""
        request = WebSocketSummarizeRequest(
            type=MessageType.TEXT_INPUT,
            content=sample_text_content["medium"],
            mode=SummarizeMode.DEFAULT,
        )
        assert request.type == MessageType.TEXT_INPUT
        assert request.content == sample_text_content["medium"]
        assert request.mode == SummarizeMode.DEFAULT


class TestAPIKeyRequest:
    """Test API key request model."""

    def test_valid_api_key_request(self, sample_api_keys):
        """Test valid API key request creation."""
        request = APIKeyRequest(**sample_api_keys["valid"])
        assert request.name == sample_api_keys["valid"]["name"]
        assert request.permissions == sample_api_keys["valid"]["permissions"]
        assert request.description == sample_api_keys["valid"]["description"]
        assert request.expires_in_days == sample_api_keys["valid"]["expires_in_days"]

    def test_api_key_request_admin_permissions(self, sample_api_keys):
        """Test API key request with admin permissions."""
        request = APIKeyRequest(**sample_api_keys["admin"])
        assert Permission.ADMIN in request.permissions
        assert Permission.READ in request.permissions
        assert Permission.WRITE in request.permissions

    def test_api_key_request_invalid_name(self):
        """Test API key request with invalid name."""
        with pytest.raises(ValidationError) as exc_info:
            APIKeyRequest(
                name="AB",  # Too short
                permissions=["read"],
            )

        errors = exc_info.value.errors()
        assert any("at least 3 characters" in str(error["msg"]) for error in errors)

    def test_api_key_request_empty_permissions(self):
        """Test API key request with empty permissions."""
        with pytest.raises(ValidationError) as exc_info:
            APIKeyRequest(
                name="Test Key",
                permissions=[],  # Empty permissions
            )

        errors = exc_info.value.errors()
        assert any("at least one permission" in str(error["msg"]) for error in errors)

    def test_api_key_request_invalid_permissions(self):
        """Test API key request with invalid permissions."""
        with pytest.raises(ValidationError) as exc_info:
            APIKeyRequest(
                name="Test Key",
                permissions=["invalid_permission"],
            )

        errors = exc_info.value.errors()
        assert any(
            "not a valid enumeration member" in str(error["msg"]) for error in errors
        )


class TestFileUploadRequest:
    """Test file upload request model."""

    def test_valid_file_upload_request(self, sample_file_uploads):
        """Test valid file upload request creation."""
        request = FileUploadRequest(**sample_file_uploads["text_file"])
        assert request.filename == sample_file_uploads["text_file"]["filename"]
        assert request.content == sample_file_uploads["text_file"]["content"]
        assert request.mime_type == sample_file_uploads["text_file"]["mime_type"]

    def test_file_upload_request_invalid_filename(self, sample_file_uploads):
        """Test file upload request with invalid filename."""
        with pytest.raises(ValidationError) as exc_info:
            FileUploadRequest(**sample_file_uploads["invalid_file"])

        errors = exc_info.value.errors()
        assert any("filename" in str(error["loc"]) for error in errors)

    def test_file_upload_request_invalid_base64(self):
        """Test file upload request with invalid base64 content."""
        with pytest.raises(ValidationError) as exc_info:
            FileUploadRequest(
                filename="test.txt",
                content="invalid-base64-content!@#",
                mime_type="text/plain",
            )

        errors = exc_info.value.errors()
        assert any("Invalid base64" in str(error["msg"]) for error in errors)
