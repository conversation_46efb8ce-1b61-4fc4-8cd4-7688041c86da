"""
Functional tests for task queue system.
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from models.schemas import TaskStatus
from worker.queue import TaskQueue
from worker.task_manager import TaskManager
from worker.task_priority import TaskPriority


class TestTaskQueue:
    """Test TaskQueue functionality."""

    @pytest.mark.asyncio
    async def test_task_queue_initialization(self):
        """Test TaskQueue initialization."""
        with patch("worker.queue.TaskManager") as mock_manager:
            mock_manager.return_value = AsyncMock()

            task_queue = TaskQueue()
            await task_queue.initialize()

            assert task_queue.task_manager is not None

    @pytest.mark.asyncio
    async def test_task_queue_shutdown(self):
        """Test TaskQueue shutdown."""
        with patch("worker.queue.TaskManager") as mock_manager:
            mock_manager_instance = AsyncMock()
            mock_manager.return_value = mock_manager_instance

            task_queue = TaskQueue()
            await task_queue.initialize()
            await task_queue.shutdown()

            # Should call shutdown on task manager
            mock_manager_instance.shutdown.assert_called_once()

    @pytest.mark.asyncio
    async def test_add_subtitle_task(self):
        """Test adding subtitle task to queue."""
        with patch("worker.queue.TaskManager") as mock_manager:
            mock_manager_instance = AsyncMock()
            mock_manager.return_value = mock_manager_instance

            # Mock response
            mock_response = MagicMock()
            mock_response.task_id = "test_task_123"
            mock_response.status = TaskStatus.PENDING
            mock_manager_instance.add_subtitle_task.return_value = mock_response

            task_queue = TaskQueue()
            await task_queue.initialize()

            response = await task_queue.add_subtitle_task(
                "https://youtube.com/watch?v=test", client_uid=None  # client_uid is deprecated
            )

            assert response.task_id == "test_task_123"
            assert response.status == TaskStatus.PENDING

    @pytest.mark.asyncio
    async def test_add_summarize_task(self):
        """Test adding summarize task to queue."""
        with patch("worker.queue.TaskManager") as mock_manager:
            mock_manager_instance = AsyncMock()
            mock_manager.return_value = mock_manager_instance

            # Mock response
            mock_response = MagicMock()
            mock_response.task_id = "summarize_task_123"
            mock_response.status = TaskStatus.PENDING
            mock_manager_instance.add_summarize_task.return_value = mock_response

            task_queue = TaskQueue()
            await task_queue.initialize()

            response = await task_queue.add_summarize_task(
                "Text to summarize", mode="default", client_uid=None  # client_uid is deprecated
            )

            assert response.task_id == "summarize_task_123"
            assert response.status == TaskStatus.PENDING

    @pytest.mark.asyncio
    async def test_add_video_list_task(self):
        """Test adding video list task to queue."""
        with patch("worker.queue.TaskManager") as mock_manager:
            mock_manager_instance = AsyncMock()
            mock_manager.return_value = mock_manager_instance

            # Mock response
            mock_response = MagicMock()
            mock_response.task_id = "video_list_task_123"
            mock_response.status = TaskStatus.PENDING
            mock_manager_instance.add_video_list_task.return_value = mock_response

            task_queue = TaskQueue()
            await task_queue.initialize()

            response = await task_queue.add_video_list_task(
                "https://youtube.com/playlist?list=test", client_uid=None  # client_uid is deprecated
            )

            assert response.task_id == "video_list_task_123"
            assert response.status == TaskStatus.PENDING

    @pytest.mark.asyncio
    async def test_get_task_status(self):
        """Test getting task status."""
        with patch("worker.queue.TaskManager") as mock_manager:
            mock_manager_instance = AsyncMock()
            mock_manager.return_value = mock_manager_instance

            # Mock response
            mock_response = MagicMock()
            mock_response.task_id = "test_task_123"
            mock_response.status = TaskStatus.COMPLETED
            mock_manager_instance.get_task_status.return_value = mock_response

            task_queue = TaskQueue()
            await task_queue.initialize()

            response = await task_queue.get_task_status("test_task_123")

            assert response.task_id == "test_task_123"
            assert response.status == TaskStatus.COMPLETED


class TestTaskManager:
    """Test TaskManager functionality."""

    def test_task_manager_creation(self):
        """Test TaskManager creation."""
        mock_ws_manager = MagicMock()
        task_manager = TaskManager(mock_ws_manager)

        assert task_manager is not None
        assert hasattr(task_manager, "queues")

    @pytest.mark.asyncio
    async def test_task_manager_initialization(self):
        """Test TaskManager initialization."""
        mock_ws_manager = MagicMock()
        task_manager = TaskManager(mock_ws_manager)

        # Should initialize without errors
        await task_manager.initialize()

        assert task_manager.is_running

    @pytest.mark.asyncio
    async def test_task_manager_shutdown(self):
        """Test TaskManager shutdown."""
        mock_ws_manager = MagicMock()
        task_manager = TaskManager(mock_ws_manager)

        await task_manager.initialize()
        await task_manager.shutdown()

        assert not task_manager.is_running

    @pytest.mark.asyncio
    async def test_task_priority_handling(self):
        """Test task priority handling."""
        mock_ws_manager = MagicMock()
        task_manager = TaskManager(mock_ws_manager)

        # Test that high priority tasks are processed first
        # This would require more detailed mocking of the queue system
        assert True  # Placeholder for priority testing

    @pytest.mark.asyncio
    async def test_worker_management(self):
        """Test worker management."""
        mock_ws_manager = MagicMock()
        task_manager = TaskManager(mock_ws_manager)

        await task_manager.initialize()

        # Should have workers for different task types
        assert hasattr(task_manager, "workers")

        await task_manager.shutdown()

    @pytest.mark.asyncio
    async def test_rate_limiting(self):
        """Test rate limiting functionality."""
        mock_ws_manager = MagicMock()
        task_manager = TaskManager(mock_ws_manager)

        # Test that rate limiting is applied
        # This would require mocking the rate limiter
        assert True  # Placeholder for rate limiting tests


class TestTaskPriority:
    """Test task priority system."""

    def test_task_priority_enum(self):
        """Test TaskPriority enum values."""
        assert TaskPriority.LOW.value == 3
        assert TaskPriority.NORMAL.value == 2
        assert TaskPriority.HIGH.value == 1
        assert TaskPriority.URGENT.value == 0

    def test_priority_comparison(self):
        """Test priority comparison."""
        # Lower values have higher priority
        assert TaskPriority.URGENT < TaskPriority.HIGH
        assert TaskPriority.HIGH < TaskPriority.NORMAL
        assert TaskPriority.NORMAL < TaskPriority.LOW

    def test_get_priority_from_string(self):
        """Test getting priority from string."""
        from worker.task_priority import get_priority_from_string

        assert get_priority_from_string("urgent") == TaskPriority.URGENT
        assert get_priority_from_string("high") == TaskPriority.HIGH
        assert get_priority_from_string("normal") == TaskPriority.NORMAL
        assert get_priority_from_string("low") == TaskPriority.LOW
        assert get_priority_from_string("invalid") == TaskPriority.NORMAL


class TestTaskHandlers:
    """Test task handler functionality."""

    @pytest.mark.asyncio
    async def test_subtitle_task_handler(self, mock_youtube_downloader):
        """Test subtitle task handler."""
        from worker.task_handlers import TaskHandler

        handler = TaskHandler()

        # Mock task data
        task_data = {
            "task_id": "test_task_123",
            "url": "https://youtube.com/watch?v=test",
            "client_uid": None,  # client_uid is deprecated
        }

        # Test handler execution
        with patch.object(handler, "handle_subtitle_task") as mock_handle:
            mock_handle.return_value = {
                "task_id": "test_task_123",
                "status": "completed",
                "title": "Test Video",
                "en_subtitles": "English content",
                "ru_subtitles": "Russian content",
            }

            result = await handler.handle_subtitle_task(task_data)
            assert result["status"] == "completed"

    @pytest.mark.asyncio
    async def test_summarize_task_handler(self, mock_text_summarizer):
        """Test summarize task handler."""
        from worker.task_handlers import TaskHandler

        handler = TaskHandler()

        # Mock task data
        task_data = {
            "task_id": "summarize_task_123",
            "text": "Long text to summarize",
            "mode": "default",
            "client_uid": None,  # client_uid is deprecated
        }

        # Test handler execution
        with patch.object(handler, "handle_summarize_task") as mock_handle:
            mock_handle.return_value = {
                "task_id": "summarize_task_123",
                "status": "completed",
                "summary": "Summarized text",
            }

            result = await handler.handle_summarize_task(task_data)
            assert result["status"] == "completed"

    @pytest.mark.asyncio
    async def test_task_handler_error_handling(self):
        """Test task handler error handling."""
        from worker.task_handlers import TaskHandler

        handler = TaskHandler()

        # Test with invalid task data
        invalid_task_data = {
            "task_id": "error_task_123",
            "invalid_field": "invalid_value",
        }

        # Should handle errors gracefully
        with patch.object(handler, "handle_subtitle_task") as mock_handle:
            mock_handle.side_effect = Exception("Test error")

            try:
                result = await handler.handle_subtitle_task(invalid_task_data)
                # Should return error status
                assert "error" in result or result.get("status") == "failed"
            except Exception:
                # Or raise exception that gets caught by queue manager
                pass


class TestTaskWatchdog:
    """Test task watchdog functionality."""

    @pytest.mark.asyncio
    async def test_task_timeout_detection(self):
        """Test task timeout detection."""
        from worker.task_watchdog import TaskWatchdog

        mock_task_manager = MagicMock()
        watchdog = TaskWatchdog(mock_task_manager)

        # Test timeout detection logic
        # This would require mocking task timestamps and timeouts
        assert True  # Placeholder for timeout tests

    @pytest.mark.asyncio
    async def test_stuck_task_detection(self):
        """Test stuck task detection."""
        from worker.task_watchdog import TaskWatchdog

        mock_task_manager = MagicMock()
        watchdog = TaskWatchdog(mock_task_manager)

        # Test stuck task detection
        # This would require mocking task states
        assert True  # Placeholder for stuck task tests

    @pytest.mark.asyncio
    async def test_task_cleanup(self):
        """Test task cleanup functionality."""
        from worker.task_watchdog import TaskWatchdog

        mock_task_manager = MagicMock()
        watchdog = TaskWatchdog(mock_task_manager)

        # Test cleanup of completed/failed tasks
        assert True  # Placeholder for cleanup tests


class TestRateLimiter:
    """Test rate limiter functionality."""

    @pytest.mark.asyncio
    async def test_youtube_rate_limiter(self):
        """Test YouTube rate limiter."""
        from worker.rate_limiter import YouTubeRateLimiter

        rate_limiter = YouTubeRateLimiter(requests_per_minute=10)

        # Test rate limiting
        for i in range(5):
            allowed = await rate_limiter.is_allowed()
            assert allowed is True

        # Test rate limit exceeded (would need to mock time)
        # This is a simplified test
        assert True

    @pytest.mark.asyncio
    async def test_summarize_rate_limiter(self):
        """Test summarize rate limiter."""
        from worker.rate_limiter import SummarizeRateLimiter

        rate_limiter = SummarizeRateLimiter(requests_per_minute=5)

        # Test rate limiting
        for i in range(3):
            allowed = await rate_limiter.is_allowed()
            assert allowed is True

    @pytest.mark.asyncio
    async def test_rate_limiter_reset(self):
        """Test rate limiter reset functionality."""
        from worker.rate_limiter import YouTubeRateLimiter

        rate_limiter = YouTubeRateLimiter(requests_per_minute=1)

        # Use up the limit
        allowed = await rate_limiter.is_allowed()
        assert allowed is True

        # Should be rate limited now
        # (In real test, would need to mock time advancement)

        # Reset should allow requests again
        rate_limiter.reset()
        allowed = await rate_limiter.is_allowed()
        assert allowed is True


class TestWebSocketNotifier:
    """Test WebSocket notifier functionality."""

    @pytest.mark.asyncio
    async def test_websocket_notification(self):
        """Test WebSocket notification."""
        from worker.ws_notifier import WebSocketNotifier

        mock_ws_manager = MagicMock()
        notifier = WebSocketNotifier(mock_ws_manager)

        # Test notification sending
        await notifier.notify_task_update(
            task_id="test_task_123", status="completed", data={"result": "test"}
        )

        # Should call WebSocket manager
        assert mock_ws_manager.send_to_client.called or True  # Placeholder

    @pytest.mark.asyncio
    async def test_websocket_notification_error_handling(self):
        """Test WebSocket notification error handling."""
        from worker.ws_notifier import WebSocketNotifier

        mock_ws_manager = MagicMock()
        mock_ws_manager.send_to_client.side_effect = Exception("Connection error")

        notifier = WebSocketNotifier(mock_ws_manager)

        # Should handle errors gracefully
        try:
            await notifier.notify_task_update(
                task_id="test_task_123", status="completed", data={"result": "test"}
            )
        except Exception:
            pytest.fail("WebSocket notifier should handle errors gracefully")


class TestTaskQueueIntegration:
    """Test task queue integration scenarios."""

    @pytest.mark.asyncio
    async def test_full_task_lifecycle(self):
        """Test complete task lifecycle."""
        # This would test the full flow from task creation to completion
        # Including queue management, worker processing, and notifications

        with patch("worker.queue.TaskManager") as mock_manager:
            mock_manager_instance = AsyncMock()
            mock_manager.return_value = mock_manager_instance

            task_queue = TaskQueue()
            await task_queue.initialize()

            # Add task
            mock_response = MagicMock()
            mock_response.task_id = "integration_test_123"
            mock_response.status = TaskStatus.PENDING
            mock_manager_instance.add_subtitle_task.return_value = mock_response

            response = await task_queue.add_subtitle_task(
                "https://youtube.com/watch?v=test"
            )

            assert response.task_id == "integration_test_123"

            # Check status
            mock_status_response = MagicMock()
            mock_status_response.task_id = "integration_test_123"
            mock_status_response.status = TaskStatus.COMPLETED
            mock_manager_instance.get_task_status.return_value = mock_status_response

            status_response = await task_queue.get_task_status("integration_test_123")
            assert status_response.status == TaskStatus.COMPLETED

            await task_queue.shutdown()

    @pytest.mark.asyncio
    async def test_concurrent_task_processing(self):
        """Test concurrent task processing."""
        # Test that multiple tasks can be processed concurrently

        with patch("worker.queue.TaskManager") as mock_manager:
            mock_manager_instance = AsyncMock()
            mock_manager.return_value = mock_manager_instance

            task_queue = TaskQueue()
            await task_queue.initialize()

            # Add multiple tasks
            tasks = []
            for i in range(5):
                mock_response = MagicMock()
                mock_response.task_id = f"concurrent_task_{i}"
                mock_response.status = TaskStatus.PENDING
                mock_manager_instance.add_subtitle_task.return_value = mock_response

                response = await task_queue.add_subtitle_task(
                    f"https://youtube.com/watch?v=test{i}"
                )
                tasks.append(response)

            # All tasks should be created
            assert len(tasks) == 5

            await task_queue.shutdown()

    @pytest.mark.asyncio
    async def test_queue_overload_handling(self):
        """Test queue overload handling."""
        # Test behavior when queue is full or overloaded

        with patch("worker.queue.TaskManager") as mock_manager:
            mock_manager_instance = AsyncMock()
            mock_manager.return_value = mock_manager_instance

            # Simulate queue full error
            mock_manager_instance.add_subtitle_task.side_effect = Exception(
                "Queue full"
            )

            task_queue = TaskQueue()
            await task_queue.initialize()

            # Should handle queue full gracefully
            with pytest.raises(Exception):
                await task_queue.add_subtitle_task("https://youtube.com/watch?v=test")

            await task_queue.shutdown()
